#!/usr/bin/env python3
"""
Test the reverted global lock fix against production-like workload.
"""

import asyncio
import time
import logging
import threading
from concurrent.futures import ThreadPoolExecutor

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_production_stability():
    """Test the global lock fix with production-like concurrent load"""
    try:
        from llm_response.initialization import get_gemma3_client, get_qwen3_client
        from shared_model import call_model_safe
        
        print("=" * 60)
        print("PRODUCTION STABILITY TEST - GLOBAL LOCK FIX")
        print("=" * 60)
        
        # Initialize models
        logger.info("Initializing models...")
        nvidia_model = get_gemma3_client()
        amd_model = get_qwen3_client()
        
        # Production-like test parameters
        test_kwargs = {"max_tokens": 10, "temperature": 0.1}
        
        def nvidia_decision_call(call_id):
            """Simulate decision system calls on AMD GPU"""
            try:
                result = call_model_safe(
                    amd_model, 
                    f"Decision {call_id}: Should <PERSON> respond to 'hello'? YES/NO", 
                    "amd_6650xt", 
                    **test_kwargs
                )
                return f"Call {call_id}: SUCCESS"
            except Exception as e:
                return f"Call {call_id}: FAILED - {e}"
        
        def amd_processing_call(call_id):
            """Simulate processing calls on NVIDIA GPU"""  
            try:
                result = call_model_safe(
                    nvidia_model,
                    f"Process {call_id}: Generate response to user message",
                    "nvidia_4070",
                    **test_kwargs
                )
                return f"Call {call_id}: SUCCESS"
            except Exception as e:
                return f"Call {call_id}: FAILED - {e}"
        
        # Test 1: High-frequency concurrent calls (like Discord transcription processing)
        print("\nTest 1: High-Frequency Concurrent Decision Calls")
        print("-" * 50)
        
        concurrent_calls = []
        with ThreadPoolExecutor(max_workers=6) as executor:
            # Simulate multiple users triggering decisions simultaneously
            for i in range(8):
                if i % 2 == 0:
                    future = executor.submit(nvidia_decision_call, f"AMD-{i}")
                else:
                    future = executor.submit(amd_processing_call, f"NVIDIA-{i}")
                concurrent_calls.append(future)
            
            # Wait for all calls to complete
            results = []
            for future in concurrent_calls:
                try:
                    result = future.result(timeout=30)  # 30 second timeout per call
                    results.append(result)
                    print(f"  {result}")
                except Exception as e:
                    error_result = f"TIMEOUT/ERROR: {e}"
                    results.append(error_result)
                    print(f"  {error_result}")
        
        # Analyze results
        successful_calls = len([r for r in results if "SUCCESS" in r])
        failed_calls = len([r for r in results if "FAILED" in r or "TIMEOUT" in r])
        
        print(f"\nResults: {successful_calls} successful, {failed_calls} failed")
        
        # Test 2: Rapid sequential calls (backend switching stress test)
        print("\nTest 2: Rapid Backend Switching")
        print("-" * 40)
        
        switch_results = []
        for i in range(6):
            try:
                # Alternate between GPUs rapidly
                if i % 2 == 0:
                    result = call_model_safe(amd_model, f"Switch {i}: AMD", "amd_6650xt", **test_kwargs)
                    switch_results.append(f"Switch {i}: AMD SUCCESS")
                else:
                    result = call_model_safe(nvidia_model, f"Switch {i}: NVIDIA", "nvidia_4070", **test_kwargs)  
                    switch_results.append(f"Switch {i}: NVIDIA SUCCESS")
                
                print(f"  {switch_results[-1]}")
                
            except Exception as e:
                error_msg = f"Switch {i}: FAILED - {e}"
                switch_results.append(error_msg)
                print(f"  {error_msg}")
        
        switch_successful = len([r for r in switch_results if "SUCCESS" in r])
        switch_failed = len([r for r in switch_results if "FAILED" in r])
        
        # Final assessment
        print("\n" + "=" * 60)
        print("STABILITY ASSESSMENT")
        print("=" * 60)
        
        total_successful = successful_calls + switch_successful
        total_failed = failed_calls + switch_failed
        total_calls = len(results) + len(switch_results)
        
        success_rate = (total_successful / total_calls) * 100 if total_calls > 0 else 0
        
        print(f"Total calls:        {total_calls}")
        print(f"Successful:         {total_successful}")
        print(f"Failed:             {total_failed}")
        print(f"Success rate:       {success_rate:.1f}%")
        
        if total_failed == 0:
            print("\nASSESSMENT: PASS - No GPU access violations detected")
            print("Global lock successfully prevents cross-GPU memory conflicts")
        elif total_failed <= 1:
            print("\nASSESSMENT: MOSTLY STABLE - Minimal failures detected")
            print("May need additional investigation but significantly improved")
        else:
            print("\nASSESSMENT: NEEDS INVESTIGATION - Multiple failures detected")
            print("Additional debugging required")
        
        return total_failed == 0
        
    except Exception as e:
        print(f"\nTEST SETUP FAILED: {e}")
        logger.error(f"Production test failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_production_stability())
