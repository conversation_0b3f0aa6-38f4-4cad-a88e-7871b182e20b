@echo off
setlocal
cd /d "%~dp0"

REM Ensure Node is available
where node >nul 2>&1 || (
  echo ERROR: Node.js is not installed or not in PATH.
  echo Install Node.js LTS from https://nodejs.org/ and re-run this script.
  pause
  exit /b 1
)

REM Install dependencies
echo Installing dependencies (this may take a few minutes)...
call npm install --no-audit --no-fund
if errorlevel 1 (
  echo ERROR: npm install failed.
  pause
  exit /b 1
)

REM Temporarily hide small icon to avoid electron-builder size check
set "ICON_FILE=icon.ico"
set "ICON_TEMP=icon.ico.skip"
if exist "%ICON_FILE%" (
  ren "%ICON_FILE" "%ICON_TEMP%" >nul 2>&1
)

REM Build Windows installer via electron-builder
echo Building Windows installer...
call npx electron-builder --win --x64
setlocal enabledelayedexpansion
if errorlevel 1 (
  echo ERROR: Build failed.
  if exist "%ICON_TEMP%" ren "%ICON_TEMP%" "%ICON_FILE%" >nul 2>&1
  pause
  exit /b 1
)

set "DIST_DIR=%CD%\dist"

if exist "%DIST_DIR%" (
  echo.
  echo Build complete. Artifacts are in:
  echo   %DIST_DIR%
  for /f "tokens=*" %%F in ('dir /b /o:-d "%DIST_DIR%\*.exe" 2^>nul') do (
    echo   %%F
    goto :after_list
  )
  :after_list
  echo.
  echo Run the Setup .exe once, then launch the app using launch.bat next time.
) else (
  echo Build completed but dist/ was not found. Check logs above.
)

pause
