#!/usr/bin/env python3
"""
Check what backends are available in llama-cpp-python
"""

import os
from load_environment import load_env_vars

# Load environment variables first
load_env_vars()

# Disable CUDA completely
os.environ["CUDA_VISIBLE_DEVICES"] = ""
os.environ["GGML_CUDA"] = "0"
os.environ["GGML_BACKEND"] = "vulkan"

try:
    from llama_cpp import Llama
    print("✅ llama-cpp-python imported successfully")
    
    # Try to get backend info
    try:
        # Check if we can access backend information
        print(f"Available backends: {getattr(Llama, 'available_backends', 'Not available')}")
    except Exception as e:
        print(f"Could not get backend info: {e}")
    
    # Try to create a simple model instance to see what backend is used
    print("\nTrying to create a minimal Llama instance...")
    try:
        # Use a minimal configuration to test backend
        test_model = Llama(
            model_path="./models/test.gguf",  # This will fail but should show backend info
            n_ctx=512,
            n_gpu_layers=0,  # CPU only for this test
            verbose=True
        )
    except FileNotFoundError:
        print("Expected FileNotFoundError - model file doesn't exist")
        print("But we should see backend initialization messages above")
    except Exception as e:
        print(f"Backend test error: {e}")
        
except ImportError as e:
    print(f"❌ Failed to import llama-cpp-python: {e}")
    print("You may need to reinstall llama-cpp-python with Vulkan support")

print("\nEnvironment variables:")
for var in ["GGML_BACKEND", "CUDA_VISIBLE_DEVICES", "GGML_CUDA", "GGML_VK_VISIBLE_DEVICES"]:
    print(f"{var}: {os.environ.get(var, 'NOT SET')}")
