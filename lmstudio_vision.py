"""
Optimized LM Studio Vision System
Fast replacement for Gemini Vision using local LM Studio with gemma-3-4b-it-qat model.
"""

import os
import logging
import asyncio
import tempfile
from datetime import datetime
from screenshot_util import optimized_screenshot_manager, analyze_image_url_fast
import pyautogui

logger = logging.getLogger(__name__)

# Use our optimized screenshot manager
vision_manager = optimized_screenshot_manager

async def analyze_screenshot(screenshot_data=None, prompt=None):
    """
    Fast screenshot analysis using LM Studio instead of Gemini.
    Compatible with the old Gemini interface but much faster.
    """
    try:
        # If we have screenshot_data from old system, ignore it and take fresh screenshot
        # Our new system is so fast it's better to just take a new one
        
        if prompt:
            # Use custom prompt
            result = await vision_manager.capture_and_analyze_fast(prompt)
        else:
            # Use default prompt
            result = await vision_manager.capture_and_analyze_fast()
        
        if result["success"]:
            logger.info(f"LM Studio screenshot analysis completed in {result.get('total_time', 0):.2f}s")
            return result["analysis"]
        else:
            error_msg = f"Screenshot analysis failed: {result.get('error', 'Unknown error')}"
            logger.error(error_msg)
            return error_msg
            
    except Exception as e:
        logger.error(f"Error in LM Studio screenshot analysis: {e}")
        return f"I encountered an issue analyzing your screenshot: {str(e)}"

async def take_and_analyze_screenshot(query=None, specific_instruction=""):
    """
    Fast screenshot taking and analysis using LM Studio.
    Compatible with Gemini interface but optimized for speed.
    """
    result = {
        "success": False,
        "screenshot": None,
        "analysis": "",
        "error": None
    }
    
    try:
        # Combine query and instruction into a single prompt
        prompt = None
        if query and specific_instruction:
            prompt = f"{query}. {specific_instruction}"
        elif query:
            prompt = query
        elif specific_instruction:
            prompt = specific_instruction
        
        # Use our optimized system
        screenshot_result = await vision_manager.capture_and_analyze_fast(prompt)
        
        if screenshot_result["success"]:
            # Generate timestamp for compatibility
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            result.update({
                "success": True,
                "screenshot": {
                    "timestamp": timestamp,
                    "filename": screenshot_result.get("filename", f"screenshots/screenshot_{timestamp}.jpg")
                },
                "analysis": screenshot_result["analysis"],
                "total_time": screenshot_result.get("total_time", 0)
            })
            
            logger.info(f"Fast screenshot analysis completed: {result['analysis'][:100]}...")
        else:
            result.update({
                "success": False,
                "error": screenshot_result.get("error", "Unknown error"),
                "analysis": screenshot_result.get("analysis", "Analysis failed")
            })
            
        return result
        
    except Exception as e:
        logger.error(f"Error in take_and_analyze_screenshot: {e}")
        result.update({
            "success": False,
            "error": str(e),
            "analysis": f"Screenshot failed: {e}"
        })
        return result

async def analyze_image_url(image_url: str) -> str | None:
    """
    Fast image URL analysis using LM Studio.
    Compatible with Gemini interface but much faster.
    """
    try:
        logger.info(f"Analyzing image URL with LM Studio: {image_url}")
        
        # Use our optimized image URL analysis
        result = await analyze_image_url_fast(
            image_url, 
            "Describe this image briefly in 2-3 sentences, focusing on the main content."
        )
        
        logger.info(f"LM Studio image URL analysis completed")
        return result
        
    except Exception as e:
        logger.error(f"Error analyzing image URL: {e}")
        return f"Could not analyze image: {e}"

# For backward compatibility with existing code
vision_model = True  # Indicate that vision is available
logger.info("LM Studio Vision system initialized successfully - fast screenshot analysis ready") 