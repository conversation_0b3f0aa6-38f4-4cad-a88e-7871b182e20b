"""
Log Viewer Client - Sends logs to Luna Log Viewer Electron app
This module provides a non-intrusive way to send logs to the Electron app
without breaking <PERSON>'s existing functionality.
"""

import asyncio
import websockets
import json
import logging
import threading
import time
from typing import Optional, Dict, Any
from datetime import datetime
import sys
import io

class LogViewerClient:
    """Client for sending logs to Luna Log Viewer Electron app"""
    
    def __init__(self, websocket_url: str = "ws://localhost:8765"):
        self.websocket_url = websocket_url
        self.websocket = None
        self.connected = False
        self.connection_lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 2  # seconds
        
        # Message queue for async sending
        self.message_queue = asyncio.Queue()
        self.loop = None
        
        # Start connection in background thread
        self.connection_thread = threading.Thread(target=self._connection_handler, daemon=True)
        self.connection_thread.start()
    
    def _connection_handler(self):
        """Handle WebSocket connection in background thread"""
        while True:
            try:
                # Create and store event loop for this thread
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)
                
                # Recreate queue for new loop
                self.message_queue = asyncio.Queue()
                
                self.loop.run_until_complete(self._maintain_connection())
            except Exception as e:
                self.logger.error(f"Connection handler error: {e}")
                time.sleep(self.reconnect_delay)
            finally:
                if self.loop:
                    self.loop.close()
                    self.loop = None
    
    async def _maintain_connection(self):
        """Maintain WebSocket connection with auto-reconnect"""
        # Start message processor task
        message_processor_task = None
        
        while True:
            try:
                if not self.connected:
                    await self._connect()
                    
                    # Start message processor when connected
                    if self.connected and message_processor_task is None:
                        message_processor_task = asyncio.create_task(self._process_message_queue())
                
                if self.websocket:
                    # Send ping to keep connection alive
                    await self.websocket.ping()
                    await asyncio.sleep(5)  # Check every 5 seconds
                else:
                    await asyncio.sleep(self.reconnect_delay)
                    
            except websockets.exceptions.ConnectionClosed:
                self.logger.warning("WebSocket connection closed, attempting reconnect...")
                self.connected = False
                self.websocket = None
                
                # Cancel message processor
                if message_processor_task:
                    message_processor_task.cancel()
                    message_processor_task = None
                    
                await asyncio.sleep(self.reconnect_delay)
            except Exception as e:
                self.logger.error(f"Connection maintenance error: {e}")
                self.connected = False
                self.websocket = None
                
                # Cancel message processor
                if message_processor_task:
                    message_processor_task.cancel()
                    message_processor_task = None
                    
                await asyncio.sleep(self.reconnect_delay)
    
    async def _connect(self):
        """Connect to WebSocket server"""
        try:
            self.websocket = await websockets.connect(
                self.websocket_url,
                ping_interval=30,
                ping_timeout=10,
                close_timeout=10
            )
            
            with self.connection_lock:
                self.connected = True
                self.reconnect_attempts = 0
            
            self.logger.info("Connected to Luna Log Viewer")
            
        except Exception as e:
            with self.connection_lock:
                self.connected = False
                self.websocket = None
                self.reconnect_attempts += 1
            
            if self.reconnect_attempts <= self.max_reconnect_attempts:
                self.logger.warning(f"Failed to connect to Log Viewer (attempt {self.reconnect_attempts}/{self.max_reconnect_attempts}): {e}")
            else:
                self.logger.error(f"Max reconnection attempts reached. Log Viewer unavailable: {e}")
    
    async def _process_message_queue(self):
        """Process messages from queue (runs in background)"""
        while self.connected:
            try:
                # Wait for message with timeout
                log_data = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                
                if self.websocket and self.connected:
                    try:
                        message = json.dumps(log_data)
                        await self.websocket.send(message)
                    except websockets.exceptions.ConnectionClosed:
                        with self.connection_lock:
                            self.connected = False
                            self.websocket = None
                        break  # Exit processor loop
                    except Exception as e:
                        self.logger.error(f"WebSocket send error: {e}")
                        
                # Mark task as done
                self.message_queue.task_done()
                        
            except asyncio.TimeoutError:
                continue  # No message, keep looping
            except Exception as e:
                self.logger.error(f"Message processor error: {e}")
                break
    
    def send_log(self, log_type: str, data: Dict[str, Any]):
        """Send log to Electron app (non-blocking)"""
        if not self.connected or not self.loop:
            return  # Silently fail if not connected
        
        try:
            # Add metadata
            log_data = {
                "type": log_type,
                "timestamp": datetime.now().isoformat(),
                **data
            }
            
            # Add to queue (thread-safe)
            asyncio.run_coroutine_threadsafe(
                self.message_queue.put(log_data),
                self.loop
            )
            
        except Exception as e:
            self.logger.error(f"Error preparing log for viewer: {e}")
    
    def send_prompt_log(self, content: str, context_type: str = "Unknown"):
        """Send a prompt log to the viewer"""
        self.send_log("prompt", {
            "content": content,
            "context_type": context_type
        })
    
    def send_transcript_log(self, user: str, message: str):
        """Send a transcript log to the viewer"""
        self.send_log("transcript", {
            "user": user,
            "message": message
        })
    
    def send_terminal_log(self, message: str, level: str = "info"):
        """Send a terminal log to the viewer"""
        self.send_log("terminal", {
            "message": message,
            "level": level.lower()
        })
    
    def send_latency_log(self, message: str = None, metrics: Dict[str, Any] = None):
        """Send a latency/performance log to the viewer"""
        data = {}
        if message:
            data["message"] = message
        if metrics:
            data["metrics"] = metrics
        
        self.send_log("latency", data)
    
    def close(self):
        """Close connection"""
        with self.connection_lock:
            self.connected = False
        
        if self.websocket:
            try:
                asyncio.run(self.websocket.close())
            except:
                pass


class TerminalLogCapture:
    """Capture terminal output and send to log viewer"""
    
    def __init__(self, log_client: LogViewerClient):
        self.log_client = log_client
        self.original_stdout = sys.stdout
        self.original_stderr = sys.stderr
        self.capturing = False
    
    def start_capture(self):
        """Start capturing terminal output"""
        if self.capturing:
            return
        
        self.capturing = True
        sys.stdout = self._create_wrapper(sys.stdout, "info")
        sys.stderr = self._create_wrapper(sys.stderr, "error")
    
    def stop_capture(self):
        """Stop capturing terminal output"""
        if not self.capturing:
            return
        
        sys.stdout = self.original_stdout
        sys.stderr = self.original_stderr
        self.capturing = False
    
    def _create_wrapper(self, original_stream, level):
        """Create a wrapper that captures output"""
        class StreamWrapper:
            def __init__(self, stream, log_client, level):
                self.stream = stream
                self.log_client = log_client
                self.level = level
                self.buffer = []
            
            def write(self, text):
                # Write to original stream
                self.stream.write(text)
                
                # Send to log viewer if it's a complete line
                if text.strip():
                    # Determine log level based on content
                    log_level = level
                    text_lower = text.lower()
                    
                    if "error" in text_lower or "exception" in text_lower:
                        log_level = "error"
                    elif "warning" in text_lower or "warn" in text_lower:
                        log_level = "warning"
                    elif "debug" in text_lower:
                        log_level = "debug"
                    else:
                        log_level = "info"
                    
                    self.log_client.send_terminal_log(text.strip(), log_level)
            
            def flush(self):
                self.stream.flush()
            
            def __getattr__(self, name):
                return getattr(self.stream, name)
        
        return StreamWrapper(original_stream, self.log_client, level)


# Global instance
_log_viewer_client: Optional[LogViewerClient] = None
_terminal_capture: Optional[TerminalLogCapture] = None

def get_log_viewer_client() -> LogViewerClient:
    """Get the global log viewer client instance"""
    global _log_viewer_client
    if _log_viewer_client is None:
        _log_viewer_client = LogViewerClient()
    return _log_viewer_client

def start_terminal_capture():
    """Start capturing terminal output for log viewer"""
    global _terminal_capture
    if _terminal_capture is None:
        _terminal_capture = TerminalLogCapture(get_log_viewer_client())
        _terminal_capture.start_capture()

def stop_terminal_capture():
    """Stop capturing terminal output"""
    global _terminal_capture
    if _terminal_capture:
        _terminal_capture.stop_capture()

# Convenience functions for easy integration
def log_prompt(content: str, context_type: str = "Unknown"):
    """Log a prompt to the viewer"""
    client = get_log_viewer_client()
    client.send_prompt_log(content, context_type)

def log_transcript(user: str, message: str):
    """Log a transcript to the viewer"""
    client = get_log_viewer_client()
    client.send_transcript_log(user, message)

def log_terminal(message: str, level: str = "info"):
    """Log terminal output to the viewer"""
    client = get_log_viewer_client()
    client.send_terminal_log(message, level)

def log_latency(message: str = None, metrics: Dict[str, Any] = None):
    """Log latency/performance data to the viewer"""
    client = get_log_viewer_client()
    client.send_latency_log(message, metrics)
