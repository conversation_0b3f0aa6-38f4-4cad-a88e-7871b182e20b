#!/usr/bin/env python3
"""
End-to-end production test for Discord bot GPU access violation fix.
This simulates the actual workflow: transcription -> decision -> brain -> response
with multiple concurrent users to verify the global GPU context lock works.
"""

import asyncio
import time
import logging
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import threading
import random

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def simulate_user_interaction(user_id, message_text):
    """Simulate a complete user interaction through the Discord bot pipeline"""
    try:
        # Import the actual Discord bot components
        from llm_response.initialization import get_gemma3_client, get_qwen3_client
        from llm_response.decision import should_respond
        from llm_brain import get_brain_decision
        from shared_model import call_model_safe
        
        logger.info(f"[USER {user_id}] Starting interaction: '{message_text[:30]}...'")
        start_time = time.time()
        
        # Step 1: Decision system (AMD GPU - Qwen3)
        logger.info(f"[USER {user_id}] Step 1: Decision processing...")
        decision_start = time.time()
        
        qwen_model = get_qwen3_client()
        decision_result = await should_respond(
            text=message_text,
            current_speaker_id=f"test_user_{user_id}",
            conversation_history=[],
            speaker_turn_history=[],
            is_currently_speaking=False
        )
        
        decision_time = time.time() - decision_start
        logger.info(f"[USER {user_id}] Decision result: {decision_result} ({decision_time:.2f}s)")
        
        # Handle both boolean and dictionary returns from decision system
        should_respond = decision_result if isinstance(decision_result, bool) else decision_result.get("should_respond", False)
        
        if not should_respond:
            logger.info(f"[USER {user_id}] Decision: No response needed")
            return {"user_id": user_id, "status": "no_response", "total_time": time.time() - start_time}
        
        # Step 2: Brain decision (AMD GPU - Qwen3)
        logger.info(f"[USER {user_id}] Step 2: Brain processing...")
        brain_start = time.time()
        
        brain_result = await get_brain_decision(
            message_text,
            user_name=f"TestUser{user_id}",
            conversation_history=[]
        )
        
        brain_time = time.time() - brain_start
        logger.info(f"[USER {user_id}] Brain result: {brain_result.get('action', 'respond')} ({brain_time:.2f}s)")
        
        # Step 3: Main response generation (NVIDIA GPU - Gemma3)
        logger.info(f"[USER {user_id}] Step 3: Response generation...")
        response_start = time.time()
        
        gemma_model = get_gemma3_client()
        
        # Simulate the actual prompt format from processing.py
        prompt = f"""<|im_start|>system
You are Luna, a friendly AI assistant.
<|im_end|>
<|im_start|>user
TestUser{user_id}: {message_text}
<|im_end|>
<|im_start|>assistant
"""
        
        response = call_model_safe(
            model=gemma_model,
            prompt=prompt,
            gpu_target="nvidia_4070",
            max_tokens=100,
            temperature=0.7,
            stop=["<|im_end|>"]
        )
        
        response_time = time.time() - response_start
        generated_text = response.get("choices", [{}])[0].get("text", "").strip()
        
        total_time = time.time() - start_time
        logger.info(f"[USER {user_id}] Response generated ({response_time:.2f}s): '{generated_text[:50]}...'")
        logger.info(f"[USER {user_id}] COMPLETE: Total time {total_time:.2f}s")
        
        return {
            "user_id": user_id,
            "status": "success",
            "decision_time": decision_time,
            "brain_time": brain_time,
            "response_time": response_time,
            "total_time": total_time,
            "response_text": generated_text[:100]
        }
        
    except Exception as e:
        total_time = time.time() - start_time
        logger.error(f"[USER {user_id}] FAILED after {total_time:.2f}s: {e}")
        return {
            "user_id": user_id,
            "status": "failed",
            "error": str(e),
            "total_time": total_time
        }

async def test_concurrent_discord_users():
    """Test multiple concurrent Discord users to stress test the GPU locks"""
    logger.info("=== Testing Concurrent Discord Users ===")
    
    # Simulate different types of messages that would trigger different processing paths
    test_messages = [
        "Hey Luna, what's the weather like?",
        "Luna, can you help me with some math?",
        "What do you think about AI ethics?",
        "Luna, tell me a joke please!",
        "How does machine learning work?",
        "Luna, what's your favorite color?",
        "Can you explain quantum physics?",
        "Luna, what's 2+2?"
    ]
    
    # Launch 6 concurrent users (more than our typical concurrent limit)
    num_users = 6
    logger.info(f"Launching {num_users} concurrent Discord user simulations...")
    
    # Use asyncio.gather for concurrent async operations
    tasks = []
    for i in range(num_users):
        user_id = i + 1
        message = random.choice(test_messages)
        task = simulate_user_interaction(user_id, message)
        tasks.append(task)
    
    # Wait for all tasks to complete
    try:
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and convert exceptions to error format
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    "user_id": i + 1,
                    "status": "failed", 
                    "error": str(result),
                    "total_time": 0
                })
            else:
                processed_results.append(result)
        
        return processed_results
        
    except Exception as e:
        logger.error(f"Concurrent test failed: {e}")
        return [{"status": "test_failed", "error": str(e)}]

async def test_rapid_user_succession():
    """Test rapid succession of users to stress test context switching"""
    logger.info("=== Testing Rapid User Succession ===")
    
    results = []
    for i in range(5):
        user_id = f"rapid_{i+1}"
        message = f"Luna, quick test {i+1}: what is {i+1}+{i+1}?"
        
        logger.info(f"Starting rapid user {i+1}/5...")
        try:
            result = await simulate_user_interaction(user_id, message)
            results.append(result)
        except Exception as e:
            results.append({
                "user_id": user_id,
                "status": "failed",
                "error": str(e),
                "total_time": 0
            })
        
        # Brief delay between users
        await asyncio.sleep(0.2)
    
    return results

def analyze_results(concurrent_results, rapid_results):
    """Analyze test results for performance and stability"""
    logger.info("=== Results Analysis ===")
    
    print("\n" + "="*80)
    print("DISCORD BOT PRODUCTION TEST RESULTS")
    print("="*80)
    
    # Concurrent users analysis
    print(f"\n[CONCURRENT USERS] {len(concurrent_results)} users")
    print("-" * 50)
    
    successful = [r for r in concurrent_results if r["status"] == "success"]
    failed = [r for r in concurrent_results if r["status"] != "success"]
    
    print(f"Success rate: {len(successful)}/{len(concurrent_results)} ({len(successful)/len(concurrent_results)*100:.1f}%)")
    
    if successful:
        avg_total = sum(r["total_time"] for r in successful) / len(successful)
        avg_decision = sum(r.get("decision_time", 0) for r in successful) / len(successful)
        avg_brain = sum(r.get("brain_time", 0) for r in successful) / len(successful)
        avg_response = sum(r.get("response_time", 0) for r in successful) / len(successful)
        
        print(f"Average times:")
        print(f"  Total: {avg_total:.2f}s")
        print(f"  Decision (AMD): {avg_decision:.2f}s")
        print(f"  Brain (AMD): {avg_brain:.2f}s") 
        print(f"  Response (NVIDIA): {avg_response:.2f}s")
    
    if failed:
        print(f"Failures:")
        for f in failed:
            print(f"  User {f['user_id']}: {f.get('error', 'Unknown error')}")
    
    # Rapid succession analysis
    print(f"\n[RAPID SUCCESSION] {len(rapid_results)} users")
    print("-" * 50)
    
    rapid_successful = [r for r in rapid_results if r["status"] == "success"]
    rapid_failed = [r for r in rapid_results if r["status"] != "success"]
    
    print(f"Success rate: {len(rapid_successful)}/{len(rapid_results)} ({len(rapid_successful)/len(rapid_results)*100:.1f}%)")
    
    if rapid_failed:
        print(f"Failures:")
        for f in rapid_failed:
            print(f"  User {f['user_id']}: {f.get('error', 'Unknown error')}")
    
    # Overall assessment
    print(f"\n[OVERALL ASSESSMENT]")
    print("-" * 50)
    
    total_tests = len(concurrent_results) + len(rapid_results)
    total_success = len(successful) + len(rapid_successful)
    overall_success_rate = total_success / total_tests * 100
    
    print(f"Overall success rate: {total_success}/{total_tests} ({overall_success_rate:.1f}%)")
    
    if overall_success_rate >= 90:
        print("[SUCCESS] GPU ACCESS VIOLATION FIX: SUCCESS")
        print("   Cross-GPU memory isolation is working properly!")
    else:
        print("[FAILED] GPU ACCESS VIOLATION FIX: NEEDS IMPROVEMENT")
        print("   Some issues remain with cross-GPU usage")
    
    return overall_success_rate >= 90

async def main():
    """Run complete Discord bot production test"""
    print("Starting Discord bot production test with GPU access violation fix...")
    
    # Test 1: Concurrent users (stress test)
    concurrent_results = await test_concurrent_discord_users()
    
    # Brief pause between tests
    await asyncio.sleep(2)
    
    # Test 2: Rapid succession (context switching test)
    rapid_results = await test_rapid_user_succession()
    
    # Analyze and report results
    success = analyze_results(concurrent_results, rapid_results)
    
    if success:
        print("\n[SUCCESS] PRODUCTION TEST PASSED!")
        print("The global GPU context lock successfully prevents access violations.")
    else:
        print("\n[WARNING] PRODUCTION TEST REVEALED ISSUES")
        print("Further investigation needed for remaining GPU conflicts.")

if __name__ == "__main__":
    asyncio.run(main())
