#!/usr/bin/env python3
"""
Test script to determine the correct KokoroEngine initialization parameters
"""

import sys
import traceback

try:
    from RealtimeTTS.engines import KokoroEngine
    print("✅ Successfully imported KokoroEngine")
    
    # Try different initialization approaches
    print("\n--- Testing KokoroEngine initialization ---")
    
    # Test 1: Empty constructor
    print("Test 1: Empty constructor")
    try:
        engine = KokoroEngine()
        print("✅ Empty constructor works!")
        print(f"Engine type: {type(engine)}")
        
        # Try to get available methods/attributes
        methods = [method for method in dir(engine) if not method.startswith('_')]
        print(f"Available methods: {methods[:10]}...")  # Show first 10 methods
        
        # Check if set_voice method exists
        if hasattr(engine, 'set_voice'):
            print("✅ set_voice method is available")
        
        del engine
        
    except Exception as e:
        print(f"❌ Empty constructor failed: {e}")
        traceback.print_exc()
    
    # Test 2: Try with debug parameter only
    print("\nTest 2: With debug parameter only")
    try:
        engine = KokoroEngine(debug=False)
        print("✅ Constructor with debug=False works!")
        del engine
    except Exception as e:
        print(f"❌ Constructor with debug failed: {e}")
    
    # Test 3: Try with various parameter names
    print("\nTest 3: Testing various parameter names")
    param_tests = [
        ('voice', 'af_nicole'),
        ('default_voice', 'af_nicole'),
        ('voice_name', 'af_nicole'),
        ('speaker', 'af_nicole'),
        ('model', 'af_nicole'),
    ]
    
    for param_name, param_value in param_tests:
        try:
            kwargs = {param_name: param_value}
            engine = KokoroEngine(**kwargs)
            print(f"✅ Constructor with {param_name}='{param_value}' works!")
            del engine
            break  # If one works, we found it
        except Exception as e:
            print(f"❌ Constructor with {param_name} failed: {e}")
    
    print("\n--- Testing complete ---")
    
except ImportError as e:
    print(f"❌ Failed to import KokoroEngine: {e}")
    print("Make sure RealtimeTTS is installed with kokoro support:")
    print("pip install RealtimeTTS[kokoro]")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    traceback.print_exc()
