# Luna Log Viewer

A beautiful Electron application for viewing Luna Discord bot logs in real-time instead of Discord channels.

## Features

- **Real-time Log Viewing**: See logs as they happen
- **Multiple Log Types**: 
  - 🎤 Voice Transcripts: Voice chat transcriptions
  - 💭 Prompts: LLM prompts and system messages
  - 💻 Terminal: Console output from main.py
  - ⏱️ Performance: Latency metrics and performance data
- **Beautiful UI**: Modern, dark-themed interface with syntax highlighting
- **Auto-scroll**: Automatically scroll to new logs (can be toggled)
- **Export Logs**: Save logs to JSON file
- **Search & Filter**: Find specific logs quickly
- **Connection Status**: Visual indicator when Luna is connected

## Setup

### Prerequisites
- Node.js (v14 or higher)
- npm

### Installation
1. Open terminal in the `luna-log-viewer` directory
2. Run: `npm install`
3. Start the app: `npm start`

Or simply double-click `start-log-viewer.bat` to automatically install and start.

### Development
- Run in development mode: `npm run dev`
- This opens DevTools for debugging

## Usage

### Starting the Log Viewer
1. Start the Electron app first (before starting Luna)
2. The app will listen on port 8765 for connections from Luna
3. Start Luna's main.py - it will automatically connect to the log viewer

### Luna Integration
The log viewer automatically receives logs from <PERSON> through a WebSocket connection when:
- `VERBOSE_PROMPT_LOGGING=true` is set in Luna's environment
- Luna starts and connects to the log viewer

### Tab Navigation
- **Prompts**: System prompts and LLM interactions
- **Transcripts**: Voice chat transcriptions with user names
- **Terminal**: Console output from Luna's main.py execution
- **Performance**: Latency metrics and performance reports

### Features
- **Auto-scroll**: Toggle the pin button in bottom-right to enable/disable automatic scrolling
- **Clear Logs**: Use the menu or buttons to clear specific tab logs or all logs
- **Export**: Save all logs to a JSON file for analysis
- **Connection Status**: Green dot = connected, orange dot = waiting

## Architecture

```
Luna Discord Bot  →  WebSocket (port 8765)  →  Electron App
     main.py              log_viewer_client.py       renderer.js
```

### Components
- **Main Process** (`main.js`): Electron main process, WebSocket server
- **Renderer Process** (`renderer.js`): UI logic, log display
- **Log Client** (`log_viewer_client.py`): Luna integration module
- **Styling** (`styles.css`): Beautiful dark theme with animations

## Configuration

The log viewer uses WebSocket port 8765 by default. This can be changed in:
- `main.js`: WebSocket server port
- `log_viewer_client.py`: Client connection URL

## Troubleshooting

### Connection Issues
- Ensure the Electron app is started before Luna
- Check that port 8765 is not being used by another application
- Look for connection messages in the terminal where you started the app

### No Logs Appearing
- Make sure `VERBOSE_PROMPT_LOGGING=true` is set in Luna's environment
- Check the connection status indicator in the app header
- Restart both Luna and the log viewer

### Performance
- The app automatically limits stored logs to prevent memory issues
- Export and clear logs periodically for best performance
- Use filters to focus on specific log types

## Development Notes

### Adding New Log Types
1. Update `log_viewer_client.py` with new log functions
2. Add log type handling in `renderer.js`
3. Update the UI tabs and styling as needed

### Customizing UI
- Modify `styles.css` for appearance changes
- Update `index.html` for structural changes
- Extend `renderer.js` for new functionality

## License
MIT License - see the main Luna project for details
