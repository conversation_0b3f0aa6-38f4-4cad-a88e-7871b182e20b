#!/usr/bin/env python3
"""
Test the GPU allocation fix - verify AMD GPU is actually being used
"""

import os
import sys
import logging
from load_environment import load_env_vars

# Load environment variables first
load_env_vars()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_decision_model_gpu():
    """Test that decision model uses AMD GPU"""
    logger.info("=" * 60)
    logger.info("TESTING DECISION MODEL GPU ALLOCATION")
    logger.info("=" * 60)
    
    try:
        from llm_response.decision import get_decision_model
        
        logger.info("Loading decision model...")
        model = get_decision_model()
        
        if model:
            logger.info("✅ Decision model loaded successfully")
            # Test generation
            response = model("Yes or no?", max_tokens=3, temperature=0.1)
            logger.info(f"Test response: {response['choices'][0]['text']}")
            logger.info("Check the verbose output above to see which GPU was used")
        else:
            logger.error("❌ Failed to load decision model")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_brain_model_gpu():
    """Test that brain model uses AMD GPU"""
    logger.info("=" * 60)
    logger.info("TESTING BRAIN MODEL GPU ALLOCATION")
    logger.info("=" * 60)
    
    try:
        from llm_brain import get_gemma_cpp_client
        
        logger.info("Loading brain model...")
        model = get_gemma_cpp_client()
        
        if model:
            logger.info("✅ Brain model loaded successfully")
            # Test generation
            response = model("Think", max_tokens=5, temperature=0.1)
            logger.info(f"Test response: {response['choices'][0]['text']}")
            logger.info("Check the verbose output above to see which GPU was used")
        else:
            logger.error("❌ Failed to load brain model")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main test function"""
    logger.info("Starting GPU allocation fix test")
    logger.info("Looking for 'Vulkan0' assignments in the output...")
    logger.info("If fix works: AMD GPU models should show 'AMD Radeon RX 6650 XT' as Vulkan0")
    logger.info("=" * 60)
    
    # Test decision model
    test_decision_model_gpu()
    
    # Test brain model  
    test_brain_model_gpu()
    
    logger.info("=" * 60)
    logger.info("GPU ALLOCATION FIX TEST COMPLETE")
    logger.info("=" * 60)
    logger.info("IMPORTANT: Check the verbose output above!")
    logger.info("- If you see 'AMD Radeon RX 6650 XT' as the device being used, the fix worked!")
    logger.info("- If you see 'NVIDIA GeForce RTX 4070', the fix failed.")

if __name__ == "__main__":
    main()
