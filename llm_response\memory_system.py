"""
Luna Memory System 2.0: Ultra-Low Latency AI Memory
==================================================

A sophisticated memory architecture inspired by mem0 but optimized for Discord
real-time interactions with <5ms retrieval times.

Features:
- Multi-tier storage (hot cache, warm storage, cold storage)
- Intelligent fact extraction and consolidation
- Graph-enhanced memory for relationship mapping
- Discord-specific optimizations (voice context, user relationships, gaming context)
- Background processing to avoid blocking responses
"""

import asyncio
import hashlib
import json
import logging
import time
import threading
from collections import defaultdict, deque
from dataclasses import dataclass, asdict
from typing import Dict, List, Optional, Tuple, Any, Set
from enum import Enum
import numpy as np
import sqlite3

# Import Luna's existing components
from .config import (
    DB_PATH, get_main_name_by_id, get_profile_by_id,
    USER_PROFILES
)
from .db_logger import log_message_to_db
from .initialization import get_gemma3_client, get_qwen3_client
from shared_model import call_qwen3_safe, call_gemma3_safe

# Import memory types system
try:
    from .memory_types import (
        MemoryType as TypedMemoryType, TypedMemoryFact, MemoryTypeManager,
        memory_type_manager, initialize_memory_types
    )
    MEMORY_TYPES_AVAILABLE = True
except ImportError:
    MEMORY_TYPES_AVAILABLE = False
    logger.warning("Memory types system not available")

logger = logging.getLogger(__name__)

class MemoryType(Enum):
    """Memory classification inspired by cognitive science"""
    WORKING = "working"        # Current conversation context
    FACTUAL = "factual"        # User preferences, facts about users  
    EPISODIC = "episodic"      # Specific conversation events
    SEMANTIC = "semantic"      # General knowledge Luna has learned
    BEHAVIORAL = "behavioral"  # How users typically interact
    RELATIONSHIP = "relationship"  # User-to-user relationships
    VOICE_CONTEXT = "voice_context"  # Voice vs text conversation context
    GAMING = "gaming"          # Minecraft and gaming-related memories

@dataclass
class MemoryFact:
    """A single extracted memory fact"""
    content: str
    memory_type: MemoryType
    user_id: Optional[int]
    channel_id: Optional[str]
    confidence: float
    timestamp: float
    source_interaction_id: Optional[int] = None
    entities: List[str] = None
    relationships: List[Tuple[str, str, str]] = None  # (entity1, relation, entity2)
    
    def __post_init__(self):
        if self.entities is None:
            self.entities = []
        if self.relationships is None:
            self.relationships = []
    
    @property
    def cache_key(self) -> str:
        """Generate a cache key for this fact"""
        content_hash = hashlib.md5(self.content.encode()).hexdigest()[:8]
        return f"{self.memory_type.value}:{self.user_id}:{content_hash}"

class HotCache:
    """Ultra-fast in-memory cache for frequently accessed memories"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 300):
        self.cache: Dict[str, Tuple[Any, float]] = {}
        self.access_count: Dict[str, int] = defaultdict(int)
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache with LRU tracking"""
        with self._lock:
            if key not in self.cache:
                return None
            
            value, timestamp = self.cache[key]
            
            # Check TTL
            if time.time() - timestamp > self.ttl_seconds:
                del self.cache[key]
                self.access_count.pop(key, None)
                return None
            
            self.access_count[key] += 1
            return value
    
    def put(self, key: str, value: Any):
        """Store item in cache with eviction if needed"""
        with self._lock:
            current_time = time.time()
            
            # Evict if at capacity
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = (value, current_time)
            self.access_count[key] = 1
    
    def _evict_lru(self):
        """Evict least recently used item"""
        if not self.cache:
            return
        
        # Find key with lowest access count
        lru_key = min(self.access_count.keys(), key=lambda k: self.access_count[k])
        del self.cache[lru_key]
        del self.access_count[lru_key]

class MemoryGraph:
    """In-memory graph for relationship mapping"""
    
    def __init__(self):
        # Adjacency list representation for speed
        self.nodes: Dict[str, Dict[str, Any]] = {}
        self.edges: Dict[str, List[Tuple[str, str, Dict[str, Any]]]] = defaultdict(list)
        self._lock = threading.RLock()
    
    def add_node(self, node_id: str, properties: Dict[str, Any]):
        """Add or update a node"""
        with self._lock:
            self.nodes[node_id] = properties
    
    def add_edge(self, from_node: str, to_node: str, relation: str, properties: Dict[str, Any] = None):
        """Add a directed edge between nodes"""
        with self._lock:
            if properties is None:
                properties = {}
            self.edges[from_node].append((to_node, relation, properties))
    
    def get_connected_facts(self, entity: str, max_depth: int = 2) -> List[Tuple[str, str, str]]:
        """Get facts connected to an entity within max_depth"""
        with self._lock:
            visited = set()
            facts = []
            queue = [(entity, 0)]
            
            while queue and len(facts) < 20:  # Limit for performance
                current_entity, depth = queue.pop(0)
                
                if current_entity in visited or depth > max_depth:
                    continue
                
                visited.add(current_entity)
                
                # Get outgoing edges
                for target, relation, props in self.edges.get(current_entity, []):
                    facts.append((current_entity, relation, target))
                    if depth < max_depth:
                        queue.append((target, depth + 1))
            
            return facts

class WarmStorage:
    """SQLite-based storage for consolidated memories with fast indexing"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or f"{DB_PATH}_memory"
        self._init_schema()
    
    def _init_schema(self):
        """Initialize the memory database schema"""
        conn = sqlite3.connect(self.db_path)
        try:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS memory_facts (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    content TEXT NOT NULL,
                    memory_type TEXT NOT NULL,
                    user_id INTEGER,
                    channel_id TEXT,
                    confidence REAL NOT NULL,
                    timestamp REAL NOT NULL,
                    source_interaction_id INTEGER,
                    entities TEXT,  -- JSON array
                    relationships TEXT,  -- JSON array
                    embedding BLOB,  -- Compressed embedding vector
                    created_at REAL NOT NULL,
                    last_accessed REAL NOT NULL,
                    access_count INTEGER DEFAULT 1
                )
            """)
            
            # Create indexes for fast retrieval
            conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_user_type ON memory_facts(user_id, memory_type)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_timestamp ON memory_facts(timestamp)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_channel ON memory_facts(channel_id)")
            conn.execute("CREATE INDEX IF NOT EXISTS idx_memory_confidence ON memory_facts(confidence)")
            
            # FTS5 for content search
            conn.execute("""
                CREATE VIRTUAL TABLE IF NOT EXISTS memory_facts_fts USING fts5(
                    content,
                    entities,
                    content='memory_facts',
                    content_rowid='id'
                )
            """)
            
            conn.commit()
        finally:
            conn.close()
    
    def store_fact(self, fact: MemoryFact) -> int:
        """Store a memory fact and return its ID"""
        conn = sqlite3.connect(self.db_path)
        try:
            current_time = time.time()
            cursor = conn.execute("""
                INSERT INTO memory_facts 
                (content, memory_type, user_id, channel_id, confidence, timestamp,
                 source_interaction_id, entities, relationships, created_at, last_accessed)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                fact.content,
                fact.memory_type.value,
                fact.user_id,
                fact.channel_id,
                fact.confidence,
                fact.timestamp,
                fact.source_interaction_id,
                json.dumps(fact.entities),
                json.dumps(fact.relationships),
                current_time,
                current_time
            ))
            
            fact_id = cursor.lastrowid
            conn.commit()
            return fact_id
        finally:
            conn.close()
    
    def search_facts(self, query: str = None, user_id: int = None, 
                    memory_type: MemoryType = None, limit: int = 10) -> List[MemoryFact]:
        """Search for relevant facts"""
        conn = sqlite3.connect(self.db_path)
        try:
            conditions = []
            params = []
            
            # Build base query
            base_sql = """
                SELECT content, memory_type, user_id, channel_id, confidence, timestamp,
                       source_interaction_id, entities, relationships
                FROM memory_facts
            """
            
            if query:
                # Smart FTS search with fallback strategy
                query_terms = query.lower().split()
                
                # Convert "your" to "luna" for personality queries
                query_terms = ['luna' if term == 'your' else term for term in query_terms]
                
                # Add semantic mapping for better term matching
                semantic_expansions = {
                    'vegetable': ['food', 'potatoes', 'loves'],
                    'vegetables': ['food', 'potatoes', 'loves'],
                    'eat': ['food', 'loves', 'potatoes'],
                    'enjoy': ['loves', 'likes', 'food'],
                    'preference': ['favorite', 'likes', 'loves'],
                    'preferences': ['favorite', 'likes', 'loves'],
                    'prefer': ['favorite', 'likes'],
                    'like': ['loves', 'likes', 'favorite']
                }
                
                # Expand query terms with semantic alternatives
                expanded_terms = []
                for term in query_terms:
                    expanded_terms.append(term)
                    if term in semantic_expansions:
                        expanded_terms.extend(semantic_expansions[term])
                
                # Remove duplicates while preserving order
                seen = set()
                query_terms = []
                for term in expanded_terms:
                    if term not in seen:
                        query_terms.append(term)
                        seen.add(term)
                
                if len(query_terms) == 1:
                    # Single term - use exact matching
                    escaped_query = query_terms[0].replace('"', '""')
                    conditions.append("id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)")
                    params.append(f'"{escaped_query}"')
                else:
                    # Multiple terms - smart fallback strategy
                    valid_terms = [term for term in query_terms if len(term) > 2]
                    
                    if valid_terms:
                        # First try: AND all terms (most precise)
                        escaped_terms = [f'"{term.replace('"', '""')}"' for term in valid_terms]
                        fts_query = ' AND '.join(escaped_terms)
                        
                        # Test if this query returns results
                        conn_test = sqlite3.connect(self.db_path)
                        try:
                            test_cursor = conn_test.execute(
                                "SELECT COUNT(*) FROM memory_facts_fts WHERE memory_facts_fts MATCH ?",
                                (fts_query,)
                            )
                            result_count = test_cursor.fetchone()[0]
                        except:
                            result_count = 0
                        finally:
                            conn_test.close()
                        
                        if result_count > 0:
                            # AND query has results, use it
                            conditions.append("id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)")
                            params.append(fts_query)
                        else:
                            # Fallback: try key terms individually with smart prioritization
                            # Prioritize specific terms over general ones
                            # High priority for person names and specific topics
                            term_priority = {
                                # Colors and preferences
                                'color': 10, 'purple': 10,
                                'food': 10, 'potatoes': 10, 'vegetable': 10, 'vegetables': 10, 'loves': 9, 'likes': 9,
                                'block': 10, 'minecraft': 10, 'cobblestone': 10, 'obsidian': 10,
                                # People names (high priority - these are often the main subject)
                                'mari': 15, 'ethan': 15, 'gavin': 15, 'alex': 15, 'sarah': 15, 'mike': 15, 'anna': 15,
                                'john': 15, 'emma': 15, 'david': 15, 'lisa': 15, 'tom': 15, 'kate': 15,
                                # Medium priority
                                'favorite': 7,
                                # Low priority (general terms)
                                'luna': 3, 'you': 3, 'user': 3
                            }
                            
                            # Sort terms by priority (higher priority first)
                            prioritized_terms = []
                            person_terms = []  # Track person names specifically
                            for term in valid_terms:
                                if term in term_priority:
                                    priority = term_priority[term]
                                    prioritized_terms.append((priority, term))
                                    # Track if this is a person name (priority 15)
                                    if priority == 15:
                                        person_terms.append(term)
                            
                            prioritized_terms.sort(reverse=True)  # Highest priority first
                            
                            # Special handling for person-specific queries
                            # If query contains person names but no facts exist about them,
                            # don't fall back to Luna facts - return no results instead
                            if person_terms:
                                # This is a person-specific query - test person terms first
                                person_found = False
                                for person_term in person_terms:
                                    test_query = f'"{person_term.replace('"', '""')}"'
                                    conn_test = sqlite3.connect(self.db_path)
                                    try:
                                        test_cursor = conn_test.execute(
                                            "SELECT COUNT(*) FROM memory_facts_fts WHERE memory_facts_fts MATCH ?",
                                            (test_query,)
                                        )
                                        test_count = test_cursor.fetchone()[0]
                                        if test_count > 0:
                                            # Found facts about this person
                                            escaped_term = f'"{person_term.replace('"', '""')}"'
                                            conditions.append("id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)")
                                            params.append(escaped_term)
                                            person_found = True
                                            break
                                    except:
                                        continue
                                    finally:
                                        conn_test.close()
                                
                                # If it's a person query but no person facts found, don't fallback
                                if not person_found:
                                    # This is a query about a specific person with no facts - return empty
                                    return []  # Explicitly return empty list instead of running query
                                
                            else:
                                # Not a person-specific query - use normal priority fallback
                                # Test each high-priority term to find one that returns results
                                successful_term = None
                                for priority, term in prioritized_terms:
                                    # Test if this term returns results
                                    test_query = f'"{term.replace('"', '""')}"'
                                    conn_test = sqlite3.connect(self.db_path)
                                    try:
                                        test_cursor = conn_test.execute(
                                            "SELECT COUNT(*) FROM memory_facts_fts WHERE memory_facts_fts MATCH ?",
                                            (test_query,)
                                        )
                                        test_count = test_cursor.fetchone()[0]
                                        if test_count > 0:
                                            successful_term = term
                                            break
                                    except:
                                        continue
                                    finally:
                                        conn_test.close()
                                
                                if successful_term:
                                    # Use the first high-priority term that has results
                                    escaped_term = f'"{successful_term.replace('"', '""')}"'
                                    conditions.append("id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)")
                                    params.append(escaped_term)
                                elif valid_terms:
                                    # Last resort: use first valid term (only for non-person queries)
                                    escaped_term = f'"{valid_terms[0].replace('"', '""')}"'
                                    conditions.append("id IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)")
                                    params.append(escaped_term)
            
            if user_id is not None:
                conditions.append("user_id = ?")
                params.append(user_id)
            
            if memory_type is not None:
                conditions.append("memory_type = ?")
                params.append(memory_type.value)
            
            where_clause = (" WHERE " + " AND ".join(conditions)) if conditions else ""
            
            sql = f"""{base_sql}{where_clause}
                ORDER BY confidence DESC, timestamp DESC
                LIMIT ?
            """
            params.append(limit)
            
            cursor = conn.execute(sql, params)
            
            facts = []
            for row in cursor.fetchall():
                fact = MemoryFact(
                    content=row[0],
                    memory_type=MemoryType(row[1]),
                    user_id=row[2],
                    channel_id=row[3],
                    confidence=row[4],
                    timestamp=row[5],
                    source_interaction_id=row[6],
                    entities=json.loads(row[7] or "[]"),
                    relationships=json.loads(row[8] or "[]")
                )
                facts.append(fact)
            
            return facts
        finally:
            conn.close()

class LunaMemorySystem:
    """Main memory system orchestrating all components"""
    
    def __init__(self):
        self.hot_cache = HotCache(max_size=1000, ttl_seconds=300)
        self.warm_storage = WarmStorage()
        self.memory_graph = MemoryGraph()
        
        # Initialize memory types system if available
        if MEMORY_TYPES_AVAILABLE:
            self.memory_type_manager = memory_type_manager
            self.typed_memory_enabled = True
            logger.info("🧠 Memory types system enabled")
        else:
            self.memory_type_manager = None
            self.typed_memory_enabled = False
        
        # Background processing
        self._background_task: Optional[asyncio.Task] = None
        self._processing_queue = asyncio.Queue()
        self._shutdown_event = asyncio.Event()
        
        # Performance tracking
        self.metrics = {
            'cache_hits': 0,
            'cache_misses': 0,
            'extraction_time': deque(maxlen=100),
            'retrieval_time': deque(maxlen=100)
        }
        
        logger.info("🧠 Luna Memory System 2.0 initialized")
    
    async def start_background_processing(self):
        """Start the background memory processing task"""
        if self._background_task is None or self._background_task.done():
            self._background_task = asyncio.create_task(self._background_processor())
            logger.info("🔄 Started background memory processing")
    
    async def stop_background_processing(self):
        """Stop the background processing gracefully"""
        self._shutdown_event.set()
        if self._background_task:
            await self._background_task
        logger.info("⏹️ Stopped background memory processing")
    
    async def process_conversation_chunk(self, messages: List[Dict[str, Any]], 
                                       user_id: int, channel_id: str):
        """Queue conversation chunk for background processing"""
        await self._processing_queue.put({
            'type': 'conversation',
            'messages': messages,
            'user_id': user_id,
            'channel_id': channel_id,
            'timestamp': time.time()
        })
    
    async def retrieve_relevant_context(self, query: str, user_id: int, 
                                      channel_id: str = None, max_facts: int = 5) -> str:
        """Ultra-fast context retrieval with <5ms target"""
        start_time = time.time()
        
        # Step 1: Check hot cache
        cache_key = f"query:{user_id}:{hashlib.md5(query.encode()).hexdigest()[:8]}"
        cached_result = self.hot_cache.get(cache_key)
        if cached_result:
            self.metrics['cache_hits'] += 1
            retrieval_time = (time.time() - start_time) * 1000
            self.metrics['retrieval_time'].append(retrieval_time)
            logger.debug(f"🎯 Cache hit: {retrieval_time:.1f}ms")
            return cached_result
        
        self.metrics['cache_misses'] += 1
        
        # Step 2: Try typed memory system first (if available)
        context_parts = []
        if self.typed_memory_enabled and self.memory_type_manager:
            try:
                # Get relevant memories by type
                typed_results = await self.memory_type_manager.retrieve_relevant_memories(
                    query=query,
                    memory_types=None,  # All types
                    entities=None,
                    user_id=user_id,
                    limit_per_type=2
                )
                
                # Convert to context
                for memory_type, memories in typed_results.items():
                    for memory in memories[:2]:  # Limit per type
                        if memory.confidence > 0.7:
                            context_parts.append(f"[{memory_type.value}] {memory.content}")
                
                if context_parts:
                    context = "\n".join(context_parts[:max_facts])
                    self.hot_cache.put(cache_key, context)
                    
                    retrieval_time = (time.time() - start_time) * 1000
                    self.metrics['retrieval_time'].append(retrieval_time)
                    logger.debug(f"🧠 Typed memory retrieval: {retrieval_time:.1f}ms, {len(context_parts)} facts")
                    return context
            
            except Exception as e:
                logger.debug(f"Typed memory retrieval failed, falling back: {e}")
        
        # Step 3: Fallback to original fast database search
        # Smart user filtering: determine if this is about Luna or about the user
        query_lower = query.lower()
        
        # Check for user-specific indicators (asking about the user, not Luna)
        user_indicators = ['my ', 'mine', 'i like', 'i love', 'i prefer', 'i said', 'i told', 'what did i']
        is_user_query = any(indicator in query_lower for indicator in user_indicators)
        
        # Check for Luna-specific indicators (asking about Luna's preferences)
        luna_indicators = ['luna', 'your ', 'you like', 'you love', 'you prefer', 'favorite', 'you enjoy', 'do you like', 'what do you']
        is_luna_query = any(indicator in query_lower for indicator in luna_indicators)
        
        # Decision logic:
        # - If it's clearly about the user ("my favorite"), filter by user
        # - If it's clearly about Luna ("luna favorite", "your favorite"), don't filter by user
        # - If ambiguous, default to user filtering for safety
        # Priority: User queries take precedence over Luna queries when both are detected
        if is_user_query:
            search_user_id = user_id  # Filter by user for user-specific queries
        elif is_luna_query and not is_user_query:
            search_user_id = None  # Don't filter by user for Luna personality queries
        else:
            search_user_id = user_id  # Default to user filtering
        
        facts = self.warm_storage.search_facts(
            query=query,
            user_id=search_user_id,
            limit=max_facts * 2  # Get extra for filtering
        )
        
        # Step 4: Graph enhancement for relationships
        entities = []
        for fact in facts:
            entities.extend(fact.entities)
        
        # Get related facts through graph traversal
        related_facts = []
        for entity in entities[:3]:  # Limit for performance
            graph_facts = self.memory_graph.get_connected_facts(entity, max_depth=1)
            related_facts.extend(graph_facts)
        
        # Step 5: Compile context
        for fact in facts[:max_facts]:
            if fact.confidence > 0.7:  # High confidence facts only
                context_parts.append(f"[{fact.memory_type.value}] {fact.content}")
        
        # Add relationship context
        if related_facts:
            relationship_context = "; ".join([
                f"{rel[0]} {rel[1]} {rel[2]}" for rel in related_facts[:3]
            ])
            context_parts.append(f"[relationships] {relationship_context}")
        
        context = "\n".join(context_parts)
        
        # Cache the result
        self.hot_cache.put(cache_key, context)
        
        retrieval_time = (time.time() - start_time) * 1000
        self.metrics['retrieval_time'].append(retrieval_time)
        logger.debug(f"🔍 Retrieved context: {retrieval_time:.1f}ms, {len(facts)} facts")
        
        return context
        self.metrics['retrieval_time'].append(retrieval_time)
        logger.debug(f"🔍 Retrieved context: {retrieval_time:.1f}ms, {len(facts)} facts")
        
        return context
    
    async def _background_processor(self):
        """Background task for processing memories without blocking responses"""
        logger.info("🔄 Background memory processor started")
        
        while not self._shutdown_event.is_set():
            try:
                # Process queued items with timeout to allow shutdown
                try:
                    item = await asyncio.wait_for(
                        self._processing_queue.get(),
                        timeout=5.0
                    )
                    await self._process_memory_item(item)
                except asyncio.TimeoutError:
                    continue  # Check shutdown event and continue
                
            except Exception as e:
                logger.error(f"Error in background memory processing: {e}", exc_info=True)
                await asyncio.sleep(1)
    
    async def _process_memory_item(self, item: Dict[str, Any]):
        """Process a single memory item"""
        if item['type'] == 'conversation':
            await self._extract_facts_from_conversation(
                item['messages'],
                item['user_id'],
                item['channel_id']
            )
    
    async def _extract_facts_from_conversation(self, messages: List[Dict[str, Any]], 
                                             user_id: int, channel_id: str):
        """Extract facts from conversation using dual-GPU setup and memory types"""
        start_time = time.time()
        
        try:
            # Prepare conversation text
            conversation_text = "\n".join([
                f"{msg.get('role', 'user')}: {msg.get('content', '')}"
                for msg in messages[-10:]  # Last 10 messages for context
            ])
            
            if len(conversation_text.strip()) < 10:
                return
            
            # Phase 1: Fast fact extraction using Qwen3 (AMD GPU)
            extraction_prompt = f"""Analyze this Discord conversation and extract key facts that should be remembered about the user or conversation context.

Conversation:
{conversation_text}

Extract facts in this format:
- [FACTUAL] User prefers X
- [BEHAVIORAL] User typically does Y
- [RELATIONSHIP] User A is friends with User B
- [GAMING] User plays Minecraft and likes building

Only extract clear, important facts. Be concise."""

            qwen3_client = get_qwen3_client()
            if qwen3_client:
                from shared_model import call_qwen3_safe
                
                # Add timeout and retry logic to prevent hanging on GPU conflicts
                async def safe_extract_facts():
                    try:
                        # Use asyncio.wait_for to add timeout protection
                        facts_text = await asyncio.wait_for(
                            asyncio.get_event_loop().run_in_executor(
                                None,
                                lambda: call_qwen3_safe(
                                    qwen3_client,
                                    extraction_prompt,
                                    max_tokens=200,
                                    temperature=0.3
                                )
                            ),
                            timeout=30.0  # 30 second timeout to prevent hanging
                        )
                        return facts_text
                    except asyncio.TimeoutError:
                        logger.warning("Memory extraction timed out - AMD GPU may be busy with decision system")
                        return None
                    except Exception as e:
                        logger.error(f"Memory extraction failed: {e}")
                        # Force cleanup on AMD GPU error
                        try:
                            import gc
                            gc.collect()
                        except:
                            pass
                        return None
                
                facts_text = await safe_extract_facts()
                
                # Skip processing if extraction failed due to GPU conflicts
                if facts_text is None:
                    logger.info("Skipping memory extraction due to AMD GPU conflicts - will retry later")
                    return
                
                if isinstance(facts_text, dict):
                    facts_text = facts_text.get('choices', [{}])[0].get('text', '')
                
                # Use memory types system if available
                if self.typed_memory_enabled and self.memory_type_manager:
                    # Parse extracted facts using advanced memory types system
                    parsed_facts = self._parse_extracted_facts_advanced(facts_text, user_id, channel_id, conversation_text)
                else:
                    # Fallback to simple parsing
                    parsed_facts = self._parse_extracted_facts(facts_text, user_id, channel_id)
                
                # Store facts
                for fact in parsed_facts:
                    fact_id = self.warm_storage.store_fact(fact)
                    
                    # Update graph with entities and relationships
                    for entity in fact.entities:
                        self.memory_graph.add_node(entity, {
                            'type': 'entity',
                            'last_seen': time.time(),
                            'user_id': user_id
                        })
                    
                    for rel in fact.relationships:
                        self.memory_graph.add_edge(rel[0], rel[2], rel[1], {
                            'fact_id': fact_id,
                            'confidence': fact.confidence
                        })
                
                extraction_time = (time.time() - start_time) * 1000
                self.metrics['extraction_time'].append(extraction_time)
                logger.debug(f"🧠 Extracted {len(parsed_facts)} facts in {extraction_time:.1f}ms")
        
        except Exception as e:
            logger.error(f"Error extracting facts: {e}", exc_info=True)
    
    def _parse_extracted_facts(self, facts_text: str, user_id: int, 
                              channel_id: str) -> List[MemoryFact]:
        """Parse facts from LLM output"""
        facts = []
        current_time = time.time()
        
        for line in facts_text.split('\n'):
            line = line.strip()
            if not line or not line.startswith('-'):
                continue
            
            # Parse format: - [TYPE] content
            try:
                # Extract type and content - look for [TYPE] pattern
                if '[' not in line or ']' not in line:
                    continue
                
                type_part = line.split('[')[1].split(']')[0].upper()
                content = line.split(']', 1)[1].strip()
                
                if not content:
                    continue
                
                # Map to memory type
                memory_type = MemoryType.FACTUAL  # Default
                if type_part == 'BEHAVIORAL':
                    memory_type = MemoryType.BEHAVIORAL
                elif type_part == 'RELATIONSHIP':
                    memory_type = MemoryType.RELATIONSHIP
                elif type_part == 'GAMING':
                    memory_type = MemoryType.GAMING
                elif type_part == 'EPISODIC':
                    memory_type = MemoryType.EPISODIC
                
                # Extract entities (simple approach)
                entities = []
                words = content.lower().split()
                for word in words:
                    if word in ['minecraft', 'potato', 'potatoes', 'gaming', 'discord']:
                        entities.append(word)
                
                # Add user name as entity if mentioned
                user_profile = get_profile_by_id(user_id)
                if user_profile:
                    user_name = user_profile.get('main_name', '').lower()
                    if user_name in content.lower():
                        entities.append(user_name)
                
                fact = MemoryFact(
                    content=content,
                    memory_type=memory_type,
                    user_id=user_id,
                    channel_id=channel_id,
                    confidence=0.8,  # Default confidence
                    timestamp=current_time,
                    entities=entities
                )
                
                facts.append(fact)
                
            except Exception as e:
                logger.debug(f"Failed to parse fact line: {line} - {e}")
                continue
        
        return facts
    
    def _parse_extracted_facts_advanced(self, facts_text: str, user_id: int, 
                                       channel_id: str, conversation_context: str) -> List[MemoryFact]:
        """Advanced fact parsing using memory types system"""
        facts = []
        current_time = time.time()
        
        # First do basic parsing
        basic_facts = self._parse_extracted_facts(facts_text, user_id, channel_id)
        
        # Then enhance with memory types classification
        if self.memory_type_manager:
            for fact in basic_facts:
                try:
                    # Create a task for typed memory processing but don't await
                    # to maintain sync nature and avoid blocking
                    def process_typed_memory():
                        try:
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            typed_memory = loop.run_until_complete(
                                self.memory_type_manager.process_and_store_memory(
                                    content=fact.content,
                                    entities=fact.entities,
                                    relationships=fact.relationships,
                                    user_id=user_id,
                                    channel_id=channel_id,
                                    conversation_context=conversation_context
                                )
                            )
                            loop.close()
                        except Exception as e:
                            logger.debug(f"Typed memory processing failed: {e}")
                    
                    # Run in background thread to avoid blocking
                    import threading
                    thread = threading.Thread(target=process_typed_memory)
                    thread.daemon = True
                    thread.start()
                    
                except Exception as e:
                    logger.debug(f"Failed to process fact with memory types: {e}")
                
                facts.append(fact)
        else:
            facts = basic_facts
        
        return facts
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics"""
        cache_total = self.metrics['cache_hits'] + self.metrics['cache_misses']
        cache_hit_rate = (self.metrics['cache_hits'] / cache_total * 100) if cache_total > 0 else 0
        
        avg_retrieval = np.mean(self.metrics['retrieval_time']) if self.metrics['retrieval_time'] else 0
        avg_extraction = np.mean(self.metrics['extraction_time']) if self.metrics['extraction_time'] else 0
        
        return {
            'cache_hit_rate': f"{cache_hit_rate:.1f}%",
            'avg_retrieval_time': f"{avg_retrieval:.1f}ms",
            'avg_extraction_time': f"{avg_extraction:.1f}ms",
            'total_cache_entries': len(self.hot_cache.cache),
            'background_queue_size': self._processing_queue.qsize()
        }

# Global memory system instance
_memory_system: Optional[LunaMemorySystem] = None

def get_memory_system() -> LunaMemorySystem:
    """Get the global memory system instance"""
    global _memory_system
    if _memory_system is None:
        _memory_system = LunaMemorySystem()
    return _memory_system

async def initialize_memory_system() -> bool:
    """Initialize the memory system and start background processing"""
    try:
        memory_system = get_memory_system()
        await memory_system.start_background_processing()
        
        # Initialize memory types system if available
        if MEMORY_TYPES_AVAILABLE:
            await initialize_memory_types()
            logger.info("✅ Memory types system initialized")
        
        logger.info("✅ Luna Memory System 2.0 initialized and running")
        return True
    except Exception as e:
        logger.error(f"❌ Failed to initialize memory system: {e}", exc_info=True)
        return False

async def shutdown_memory_system():
    """Gracefully shutdown the memory system"""
    global _memory_system
    if _memory_system:
        await _memory_system.stop_background_processing()
        _memory_system = None
        logger.info("✅ Memory system shutdown complete")