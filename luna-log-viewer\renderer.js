const { ipc<PERSON><PERSON><PERSON> } = require('electron');

// State management
let logs = {
    prompts: [],
    transcripts: [],
    terminal: [],
    latency: [],
    screenshots: []
};

let autoScroll = true;
let currentTab = 'prompts';
let avatarRegistry = { by_id: {}, by_name: {} };

// Initialize app
document.addEventListener('DOMContentLoaded', () => {
    console.log('Luna Log Viewer renderer started');
    
    // Set up IPC listeners
    setupIPCListeners();
    // Load avatar registry
    loadAvatarRegistry();
    
    // Update connection status periodically
    updateConnectionStatus();
    setInterval(updateConnectionStatus, 5000);
    
    // Update log counts
    updateLogCounts();

    // Global Ctrl+F to focus terminal search when on terminal tab
    document.addEventListener('keydown', (e) => {
        const isCtrlF = (e.ctrlKey || e.metaKey) && e.key.toLowerCase() === 'f';
        if (isCtrlF) {
            const input = document.getElementById('terminal-search');
            if (currentTab === 'terminal' && input) {
                e.preventDefault();
                input.focus();
                input.select();
            }
        }
    });

    // Avatar manager button (robust: delegate to body so it works even if button is re-rendered)
    document.body.addEventListener('click', (e) => {
        const target = e.target.closest && e.target.closest('#manage-avatars-btn');
        if (target) {
            e.preventDefault();
            try { openAvatarManager(); } catch (err) { console.error('Failed to open avatar manager:', err); }
        }
    }, true);
});

// ===================== Avatar helpers =====================
async function loadAvatarRegistry() {
    try {
        const reg = await ipcRenderer.invoke('avatars:load');
        if (reg && typeof reg === 'object') {
            avatarRegistry = {
                by_id: reg.by_id || {},
                by_name: reg.by_name || {}
            };
        }
    } catch (e) {
        console.warn('Failed to load avatar registry:', e);
    }
}

async function saveAvatarRegistry() {
    try {
        await ipcRenderer.invoke('avatars:save', avatarRegistry);
    } catch (e) {
        console.warn('Failed to save avatar registry:', e);
    }
}

function resolveAvatarFor(userId, userName) {
    if (userId && avatarRegistry.by_id[userId]) return avatarRegistry.by_id[userId];
    if (userName) {
        const key = userName.trim().toLowerCase();
        if (avatarRegistry.by_name[key]) return avatarRegistry.by_name[key];
    }
    return '';
}

async function openAvatarManager(targetUserId = '', targetUserName = '') {
    // Build lightweight modal UI (no window.prompt)
    const existing = document.getElementById('avatar-modal-overlay');
    if (existing) existing.remove();

    const overlay = document.createElement('div');
    overlay.id = 'avatar-modal-overlay';
    overlay.style.position = 'fixed';
    overlay.style.inset = '0';
    overlay.style.background = 'rgba(0,0,0,0.45)';
    overlay.style.display = 'flex';
    overlay.style.alignItems = 'center';
    overlay.style.justifyContent = 'center';
    overlay.style.zIndex = '9999';

    const panel = document.createElement('div');
    panel.style.width = 'min(980px, 96vw)';
    panel.style.background = '#1f2430';
    panel.style.color = '#eef2f7';
    panel.style.border = '1px solid #2c3342';
    panel.style.borderRadius = '12px';
    panel.style.boxShadow = '0 12px 40px rgba(0,0,0,0.5)';
    panel.style.padding = '18px 18px 12px 18px';
    panel.style.maxHeight = '90vh';
    panel.style.overflow = 'auto';

    const title = document.createElement('div');
    title.style.display = 'flex';
    title.style.alignItems = 'center';
    title.style.justifyContent = 'space-between';
    title.innerHTML = `<h3 style="margin:0;font-size:18px">Manage Avatar</h3>`;

    // Layout: left list, right form
    const body = document.createElement('div');
    body.style.display = 'grid';
    body.style.gridTemplateColumns = 'minmax(220px, 32%) 1fr';
    body.style.gap = '14px';
    body.style.marginTop = '12px';
    body.style.maxHeight = 'calc(90vh - 140px)';
    body.style.overflow = 'hidden';

    const listWrap = document.createElement('div');
    listWrap.style.border = '1px solid #2c3342';
    listWrap.style.borderRadius = '10px';
    listWrap.style.overflow = 'hidden';
    listWrap.style.display = 'flex';
    listWrap.style.flexDirection = 'column';
    listWrap.style.minHeight = '300px';
    listWrap.style.maxHeight = '100%';

    const listHeader = document.createElement('div');
    listHeader.style.padding = '8px 10px';
    listHeader.style.background = '#202738';
    listHeader.style.fontSize = '12px';
    listHeader.style.color = '#9aa7bd';
    listHeader.textContent = 'Users (from config, transcripts, and saved)';

    const searchBox = document.createElement('input');
    searchBox.type = 'text';
    searchBox.placeholder = 'Search name or ID';
    searchBox.style.margin = '8px 8px 0 8px';
    searchBox.style.padding = '8px 10px';
    searchBox.style.borderRadius = '8px';
    searchBox.style.border = '1px solid #2c3342';
    searchBox.style.background = '#151a24';
    searchBox.style.color = '#eef2f7';

    const list = document.createElement('div');
    list.id = 'avatar-user-list';
    list.style.flex = '1';
    list.style.overflow = 'auto';
    list.style.padding = '8px';

listWrap.appendChild(listHeader);
listWrap.appendChild(searchBox);
listWrap.appendChild(list);

const form = document.createElement('div');
form.style.overflow = 'auto';
form.style.maxHeight = '100%';
form.innerHTML = `
    <div style="display:grid;grid-template-columns:1fr 1fr;gap:10px;margin-bottom:10px;">
        <div>
            <label style="display:block;font-size:12px;color:#9aa7bd;margin-bottom:6px;">Username</label>
            <input id="avatar-username" type="text" placeholder="e.g. John Doe" style="width:100%;padding:8px 10px;border-radius:8px;border:1px solid #2c3342;background:#151a24;color:#eef2f7;">
        </div>
        <div>
            <label style="display:block;font-size:12px;color:#9aa7bd;margin-bottom:6px;">User ID</label>
            <input id="avatar-userid" type="text" placeholder="numeric id" style="width:100%;padding:8px 10px;border-radius:8px;border:1px solid #2c3342;background:#151a24;color:#eef2f7;">
        </div>
    </div>
    <div style="margin-bottom:10px;">
        <label style="display:block;font-size:12px;color:#9aa7bd;margin-bottom:6px;">Aliases (comma-separated)</label>
        <textarea id="avatar-aliases" placeholder="e.g. ethan, uwu" rows="2" style="width:100%;padding:8px 10px;border-radius:8px;border:1px solid #2c3342;background:#151a24;color:#eef2f7;resize:vertical;"></textarea>
    </div>
    <div style="display:grid;grid-template-columns:1fr 120px;gap:12px;align-items:end;">
        <div>
            <label style="display:block;font-size:12px;color:#9aa7bd;margin-bottom:6px;">Image URL</label>
            <input id="avatar-url" type="text" placeholder="Paste an image URL or use Pick Local" style="width:100%;padding:8px 10px;border-radius:8px;border:1px solid #2c3342;background:#151a24;color:#eef2f7;">
        </div>
        <div id="avatar-preview" style="width:120px;height:120px;border-radius:12px;border:1px solid #2c3342;background:#0f141d;display:flex;align-items:center;justify-content:center;overflow:hidden;">
            <span style="font-size:12px;color:#9aa7bd;">No Preview</span>
        </div>
    </div>
    <div id="avatar-hint" style="margin-top:8px;font-size:12px;color:#9aa7bd;">Tip: You can leave URL empty and click Pick Local to choose a file.</div>
`;

body.appendChild(listWrap);
body.appendChild(form);

const buttons = document.createElement('div');
buttons.style.display = 'flex';
buttons.style.gap = '8px';
buttons.style.justifyContent = 'flex-end';
buttons.style.marginTop = '14px';

const btnNew = document.createElement('button');
btnNew.textContent = 'New User';
btnNew.className = 'btn';
btnNew.style.padding = '8px 12px';

const btnSaveProfile = document.createElement('button');
btnSaveProfile.textContent = 'Save Profile';
btnSaveProfile.className = 'btn btn-secondary';
btnSaveProfile.style.padding = '8px 12px';

const btnRemove = document.createElement('button');
btnRemove.textContent = 'Remove Avatar';
btnRemove.className = 'btn btn-ghost';
btnRemove.style.padding = '8px 12px';

const btnPick = document.createElement('button');
btnPick.textContent = 'Pick Local';
btnPick.className = 'btn btn-secondary';
btnPick.style.padding = '8px 12px';

const btnCancel = document.createElement('button');
btnCancel.textContent = 'Cancel';
btnCancel.className = 'btn';
btnCancel.style.padding = '8px 12px';

const btnSave = document.createElement('button');
btnSave.textContent = 'Save Avatar';
btnSave.className = 'btn btn-primary';
btnSave.style.padding = '8px 12px';

buttons.appendChild(btnNew);
buttons.appendChild(btnSaveProfile);
buttons.appendChild(btnRemove);
buttons.appendChild(btnPick);
buttons.appendChild(btnCancel);
buttons.appendChild(btnSave);

panel.appendChild(title);
panel.appendChild(body);
panel.appendChild(buttons);
overlay.appendChild(panel);
document.body.appendChild(overlay);

    // Prefill fields and wire minimal interactions
    const $ = (sel) => overlay.querySelector(sel);
    $('#avatar-username').value = (targetUserName || '').toString();
    $('#avatar-userid').value = (targetUserId || '').toString();

    function updatePreview(url, name) {
        const prev = $('#avatar-preview');
        prev.innerHTML = '';
        if (url) {
            const img = document.createElement('img');
            img.src = url;
            img.alt = name || '';
            img.style.maxWidth = '100%';
            img.style.maxHeight = '100%';
            prev.appendChild(img);
        } else {
            prev.innerHTML = '<span style="font-size:12px;color:#9aa7bd;">No Preview</span>';
        }
    }
    const initialAvatar = resolveAvatarFor(targetUserId, targetUserName);
    $('#avatar-url').value = initialAvatar || '';
    updatePreview(initialAvatar || '', targetUserName || '');

    function close() { overlay.remove(); }
    btnCancel.addEventListener('click', close);
    overlay.addEventListener('click', (e) => { if (e.target === overlay) close(); });

    btnNew.addEventListener('click', () => {
        $('#avatar-username').value = '';
        $('#avatar-userid').value = '';
        $('#avatar-aliases').value = '';
        $('#avatar-url').value = '';
        updatePreview('', '');
        $('#avatar-username').focus();
    });

    btnSaveProfile.addEventListener('click', async () => {
        const uid = ($('#avatar-userid').value || '').trim();
        const uname = ($('#avatar-username').value || '').trim();
        const aliases = ($('#avatar-aliases').value || '').split(',').map(s=>s.trim()).filter(Boolean);
        if (!uid || !/^[0-9]+$/.test(uid)) {
            $('#avatar-hint').textContent = 'Enter a numeric User ID to save profile.';
            $('#avatar-hint').style.color = '#f0ad4e';
            return;
        }
        if (!uname) {
            $('#avatar-hint').textContent = 'Enter a Username (main name).';
            $('#avatar-hint').style.color = '#f0ad4e';
            return;
        }
        try {
            const res = await ipcRenderer.invoke('avatars:saveKnownUserProfile', { id: uid, main_name: uname, aliases });
            if (!res || !res.success) throw new Error(res && res.error ? res.error : 'unknown error');
            $('#avatar-hint').textContent = 'Profile saved.';
            $('#avatar-hint').style.color = '#9aa7bd';
        } catch (err) {
            $('#avatar-hint').textContent = 'Failed saving profile: ' + err.message;
            $('#avatar-hint').style.color = '#f0ad4e';
        }
    });

    // Live preview when URL changes
    $('#avatar-url').addEventListener('input', (e) => {
        const url = (e.target.value || '').trim();
        const name = ($('#avatar-username').value || '').trim();
        updatePreview(url, name);
    });

    // Pick Local: open dialog via main, fill URL field and preview
    btnPick.addEventListener('click', async () => {
        try {
            const uid = ($('#avatar-userid').value || '').trim();
            const uname = ($('#avatar-username').value || '').trim();
            const res = await ipcRenderer.invoke('avatars:pickImage', { forUserId: uid, forUserName: uname });
            if (res && !res.canceled && res.path) {
                $('#avatar-url').value = res.path;
                updatePreview(res.path, uname);
                $('#avatar-hint').textContent = 'Local image selected. Click Save Avatar to apply.';
                $('#avatar-hint').style.color = '#9aa7bd';
            }
        } catch (e) {
            $('#avatar-hint').textContent = 'Image pick failed: ' + (e?.message || String(e));
            $('#avatar-hint').style.color = '#f0ad4e';
        }
    });

    // Save Avatar: persist mapping in avatarRegistry and save to disk
    btnSave.addEventListener('click', async () => {
        const uid = ($('#avatar-userid').value || '').trim();
        const uname = ($('#avatar-username').value || '').trim();
        const url = ($('#avatar-url').value || '').trim();
        if (!uid && !uname) {
            $('#avatar-hint').textContent = 'Enter a User ID or Username before saving avatar.';
            $('#avatar-hint').style.color = '#f0ad4e';
            return;
        }
        if (!url) {
            $('#avatar-hint').textContent = 'Provide an Image URL or Pick Local before saving avatar.';
            $('#avatar-hint').style.color = '#f0ad4e';
            return;
        }
        try {
            // Ensure registry structure
            if (!avatarRegistry || typeof avatarRegistry !== 'object') avatarRegistry = { by_id: {}, by_name: {} };
            if (!avatarRegistry.by_id) avatarRegistry.by_id = {};
            if (!avatarRegistry.by_name) avatarRegistry.by_name = {};

            // Prefer ID mapping when present; else use name mapping (lowercased)
            if (uid && /^[0-9]+$/.test(uid)) {
                avatarRegistry.by_id[uid] = url;
            }
            if (uname) {
                const nameKey = uname.trim().toLowerCase();
                if (nameKey) avatarRegistry.by_name[nameKey] = url;
            }

            const result = await ipcRenderer.invoke('avatars:save', avatarRegistry);
            if (!result || !result.success) throw new Error('Failed to save avatar registry');

            updatePreview(url, uname);
            $('#avatar-hint').textContent = 'Avatar saved.';
            $('#avatar-hint').style.color = '#9aa7bd';
            // Refresh list avatars
            try { renderList(searchBox.value || ''); } catch {}
        } catch (e) {
            $('#avatar-hint').textContent = 'Failed to save avatar: ' + (e?.message || String(e));
            $('#avatar-hint').style.color = '#f0ad4e';
        }
    });

    // Remove Avatar: clear mapping for current user
    btnRemove.addEventListener('click', async () => {
        const uid = ($('#avatar-userid').value || '').trim();
        const uname = ($('#avatar-username').value || '').trim();
        try {
            if (avatarRegistry?.by_id && uid && avatarRegistry.by_id[uid]) delete avatarRegistry.by_id[uid];
            if (avatarRegistry?.by_name && uname) {
                const key = uname.trim().toLowerCase();
                if (key && avatarRegistry.by_name[key]) delete avatarRegistry.by_name[key];
            }
            const result = await ipcRenderer.invoke('avatars:save', avatarRegistry || { by_id: {}, by_name: {} });
            if (!result || !result.success) throw new Error('Failed to save avatar registry');
            $('#avatar-url').value = '';
            updatePreview('', uname);
            $('#avatar-hint').textContent = 'Avatar removed.';
            $('#avatar-hint').style.color = '#9aa7bd';
            try { renderList(searchBox.value || ''); } catch {}
        } catch (e) {
            $('#avatar-hint').textContent = 'Failed to remove avatar: ' + (e?.message || String(e));
            $('#avatar-hint').style.color = '#f0ad4e';
        }
    });

    // Load known users from config.py and MERGE with transcript participants and avatar registry
    // Display: only main names on the left (no IDs/aliases). Search still matches all.
    const byKey = new Map();
    const addUser = (id, main_name, aliases = []) => {
        const key = id && /^[0-9]+$/.test(id) ? `id:${id}` : `name:${(main_name || '').trim().toLowerCase()}`;
        if (!key || key === 'name:') return;
        if (!byKey.has(key)) byKey.set(key, { id: id || '', main_name: (main_name || '').toString(), aliases: Array.isArray(aliases) ? aliases : [] });
        else {
            const u = byKey.get(key);
            if (!u.main_name && main_name) u.main_name = main_name;
            if (!u.id && id) u.id = id;
            if (aliases && aliases.length) {
                const set = new Set([...(u.aliases || []), ...aliases]);
                u.aliases = Array.from(set).filter(Boolean);
            }
        }
    };

    // 1) Known users from config.py (handler returns { success, users: [{id, main_name, aliases}] })
    try {
        const resp = await ipcRenderer.invoke('avatars:listKnownUsers');
        if (resp && resp.success && Array.isArray(resp.users)) {
            for (const u of resp.users) {
                addUser((u.id || '').toString(), (u.main_name || '').toString(), Array.isArray(u.aliases) ? u.aliases : []);
            }
        }
    } catch (err) {
        console.warn('Failed to load known users:', err);
    }

    // 2) Participants from transcripts (current session logs)
    try {
        (logs.transcripts || []).forEach(t => {
            const tid = (t.user_id || t.userId || '').toString();
            const tname = (t.user || t.username || '').toString();
            if (tid || tname) addUser(tid, tname);
        });
    } catch (e) {
        console.warn('Failed to merge transcript users:', e);
    }

    // 3) Avatar registry entries
    try {
        // by_id: we have id -> url, but no name; keep placeholder name empty (filled by other sources if available)
        if (avatarRegistry && avatarRegistry.by_id) {
            for (const id of Object.keys(avatarRegistry.by_id)) addUser(id, '');
        }
        // by_name: we have names without ids
        if (avatarRegistry && avatarRegistry.by_name) {
            for (const name of Object.keys(avatarRegistry.by_name)) addUser('', name);
        }
    } catch (e) {
        console.warn('Failed to merge avatar registry users:', e);
    }

    let users = Array.from(byKey.values())
        .map(u => ({ id: u.id || '', main_name: (u.main_name || '').toString(), aliases: Array.isArray(u.aliases) ? u.aliases : [] }))
        // stable sort: names A-Z, then IDs so list is deterministic
        .sort((a, b) => (a.main_name.toLowerCase() || '').localeCompare(b.main_name.toLowerCase() || '') || (a.id || '').localeCompare(b.id || ''));

    const usersList = $('#avatar-user-list');
    const renderList = (query = '') => {
        const q = (query || '').toLowerCase();
        usersList.innerHTML = '';
        const filtered = users.filter(u => {
            if (!q) return true;
            const name = (u.main_name || '').toLowerCase();
            const id = (u.id || '').toLowerCase();
            const aliases = (u.aliases || []).join(',').toLowerCase();
            return name.includes(q) || id.includes(q) || aliases.includes(q);
        });
        if (filtered.length === 0) {
            usersList.innerHTML = '<div style="color:#9aa7bd;font-size:12px;padding:8px;">No users match your search.</div>';
            return;
        }
        filtered.forEach(u => {
            const row = document.createElement('div');
            row.style.display = 'grid';
            row.style.gridTemplateColumns = '1fr 26px';
            row.style.gap = '8px';
            row.style.alignItems = 'center';
            row.style.padding = '8px';
            row.style.borderRadius = '8px';
            row.style.cursor = 'pointer';
            row.style.border = '1px solid transparent';
            row.addEventListener('mouseenter', () => row.style.borderColor = '#2c3342');
            row.addEventListener('mouseleave', () => row.style.borderColor = 'transparent');

            const left = document.createElement('div');
            left.style.overflow = 'hidden';
            left.style.whiteSpace = 'nowrap';
            left.style.textOverflow = 'ellipsis';
            left.textContent = u.main_name || '(Unnamed)';

            const right = document.createElement('div');
            right.style.width = '26px';
            right.style.height = '26px';
            right.style.borderRadius = '50%';
            right.style.border = '1px solid #2c3342';
            right.style.overflow = 'hidden';
            right.style.display = 'flex';
            right.style.alignItems = 'center';
            right.style.justifyContent = 'center';
            const avatar = resolveAvatarFor(u.id, u.main_name);
            if (avatar) {
                const img = document.createElement('img');
                img.src = avatar;
                img.alt = u.main_name || '';
                img.style.maxWidth = '100%';
                img.style.maxHeight = '100%';
                right.appendChild(img);
            } else {
                right.innerHTML = '<span style="color:#9aa7bd;font-size:11px;">NA</span>';
            }

            row.appendChild(left);
            row.appendChild(right);
            row.addEventListener('click', () => {
                $('#avatar-username').value = u.main_name || '';
                $('#avatar-userid').value = u.id || '';
                $('#avatar-aliases').value = (u.aliases || []).join(', ');
                const existing = resolveAvatarFor(u.id, u.main_name);
                $('#avatar-url').value = existing || '';
                updatePreview(existing || '', u.main_name || '');
            });
            usersList.appendChild(row);
        });
    };

    searchBox.addEventListener('input', () => renderList(searchBox.value || ''));
    renderList('');

    // Expose for external callers
    if (typeof window !== 'undefined') {
        window.openAvatarManager = openAvatarManager;
    }
}

function setupIPCListeners() {
    // Listen for new logs from main process
    ipcRenderer.on('new-log', (event, logData) => {
        console.log('Received log:', logData);
        addLog(logData);
    });
    
    // Listen for clear logs command
    ipcRenderer.on('clear-logs', () => {
        clearAllLogs();
    });

    // Open Avatar Manager via UI command from main (tray/menu)
    ipcRenderer.on('ui-open-avatar-manager', () => {
        try { openAvatarManager(); } catch (e) { console.error('openAvatarManager failed:', e); }
    });

    // Start/Stop Luna from tray/menu
    ipcRenderer.on('ui-start-luna', () => {
        try { startLuna(); } catch (e) { console.error('startLuna failed:', e); }
    });
    ipcRenderer.on('ui-stop-luna', () => {
        try { stopLuna(); } catch (e) { console.error('stopLuna failed:', e); }
    });

    // Listen for export logs command
    ipcRenderer.on('export-logs', () => {
        exportLogs();
    });
}

function createScreenshotLogElement(data) {
    const div = document.createElement('div');
    div.className = 'log-entry screenshot-log';

    const stage = data.stage || 'result';
    const source = data.source || 'manual';
    const processing = typeof data.processing_time === 'number' ? ` (${data.processing_time.toFixed(2)}s)` : '';
    const prompt = data.prompt ? `<div class="screenshot-prompt"><strong>Prompt:</strong> ${escapeHtml(data.prompt)}</div>` : '';

    div.innerHTML = `
        <div class="log-header">
            <div class="log-title">
                <span class="log-type-badge screenshot">Screenshot</span>
                <span>${stage === 'start' ? 'Analyzing screen…' : 'Analysis Result'}</span>
                <span class="screenshot-meta">• ${escapeHtml(source)}${processing}</span>
            </div>
            <div class="log-timestamp">${formatTimestamp(data.timestamp)}</div>
        </div>
        <div class="log-content">
            <pre><code>${escapeHtml(data.content || data.message || '')}</code></pre>
            ${prompt}
        </div>
    `;
    return div;
}

function addLog(logData) {
    const { type, ...data } = logData;
    
    // Add timestamp if not present
    if (!data.timestamp) {
        data.timestamp = new Date().toISOString();
    }
    
    // Add to appropriate log array
    switch (type) {
        case 'prompt':
            logs.prompts.push(data);
            renderLog('prompts', data);
            break;
        case 'transcript':
            logs.transcripts.push(data);
            renderLog('transcripts', data);
            break;
        case 'terminal':
            logs.terminal.push(data);
            renderLog('terminal', data);
            break;
        case 'latency':
            logs.latency.push(data);
            renderLog('latency', data);
            break;
        case 'screenshot_analysis':
            logs.screenshots.push(data);
            renderLog('screenshots', data);
            break;
        default:
            console.warn('Unknown log type:', type);
    }
    
    updateLogCounts();
    
    // Only re-render the whole tab if a filter/search is active;
    // otherwise keep appended items to preserve collapse states.
    if (type === 'prompt' && _filters?.prompts && _filters.prompts !== 'all') {
        applyActiveFilters('prompts');
    } else if (type === 'transcript' && _filters?.transcriptsQuery && _filters.transcriptsQuery.trim() !== '') {
        applyActiveFilters('transcripts');
    } else if (type === 'terminal' && _filters?.terminal && _filters.terminal !== 'all') {
        applyActiveFilters('terminal');
    }

    // Auto-scroll page when viewing the affected tab
    const tabMap = { prompt: 'prompts', transcript: 'transcripts', terminal: 'terminal', latency: 'latency', screenshot_analysis: 'screenshots' };
    const tabName = tabMap[type];
    if (autoScroll && currentTab === tabName) {
        scrollToBottom();
    }
}

function renderLog(tabType, data) {
    const container = document.getElementById(`${tabType}-container`);
    
    // Remove empty state if present
    const emptyState = container.querySelector('.empty-state');
    if (emptyState) {
        emptyState.remove();
    }
    
    let logElement;
    
    switch (tabType) {
        case 'prompts':
            logElement = createPromptLogElement(data);
            break;
        case 'transcripts':
            logElement = createTranscriptLogElement(data);
            break;
        case 'terminal':
            logElement = createTerminalLogElement(data);
            break;
        case 'latency':
            logElement = createLatencyLogElement(data);
            break;
        case 'screenshots':
            logElement = createScreenshotLogElement(data);
            break;
    }
    
    if (logElement) {
        container.appendChild(logElement);
    }
}

function createPromptLogElement(data) {
    const div = document.createElement('div');
    // Add identifiable class for prompt-specific styling (scrolling/ wrapping)
    // and default to collapsed to avoid squashing the page
    div.className = 'log-entry prompt-log collapsed';
    div.innerHTML = `
        <div class="log-header">
            <button class="collapse-toggle" aria-expanded="false" title="Expand prompt"></button>
            <div class="log-title">
                <span class="log-type-badge ${data.context_type?.toLowerCase().replace(' ', '-') || 'unknown'}">
                    ${data.context_type || 'Unknown'}
                </span>
                <span>Prompt Log</span>
            </div>
            <div class="log-timestamp">${formatTimestamp(data.timestamp)}</div>
        </div>
        <div class="log-content">
            <pre><code>${escapeHtml(data.content || data.message || 'No content')}</code></pre>
        </div>
    `;

    // Wire up collapse/expand behavior
    const toggleBtn = div.querySelector('.collapse-toggle');
    const contentEl = div.querySelector('.log-content');
    const setCollapsed = (collapsed) => {
        div.classList.toggle('collapsed', collapsed);
        toggleBtn.setAttribute('aria-expanded', (!collapsed).toString());
        toggleBtn.title = collapsed ? 'Expand prompt' : 'Collapse prompt';
    };
    setCollapsed(true);
    const toggle = () => {
        const nowCollapsed = div.classList.contains('collapsed');
        setCollapsed(!nowCollapsed);
        if (autoScroll && currentTab === 'prompts' && !div.classList.contains('collapsed')) {
            scrollToBottom();
        }
    };
    toggleBtn.addEventListener('click', toggle);
    const titleEl = div.querySelector('.log-title');
    if (titleEl) titleEl.addEventListener('click', toggle);
    return div;
}

function createTranscriptLogElement(data) {
    const div = document.createElement('div');
    div.className = 'transcript-entry';

    const userName = data.user || data.username || 'Unknown User';
    const userId = data.user_id || data.userId || '';
    const initials = userName.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase();
    const avatarUrl = resolveAvatarFor(userId, userName) || data.avatar_url || data.avatar || '';

    const avatarHtml = avatarUrl
        ? `<img src="${escapeHtml(avatarUrl)}" alt="${escapeHtml(userName)}" />`
        : `${escapeHtml(initials)}`;
    
    div.innerHTML = `
        <div class="transcript-avatar">${avatarHtml}</div>
        <div class="transcript-content">
            <div class="transcript-header">
                <span class="transcript-user">${escapeHtml(userName)}</span>
                <span class="transcript-timestamp">${formatTimestamp(data.timestamp)}</span>
            </div>
            <div class="transcript-text">${escapeHtml(data.message || data.content || '')}</div>
        </div>
    `;

    // Quick-assign avatar on click
    const avatarEl = div.querySelector('.transcript-avatar');
    if (avatarEl) {
        avatarEl.style.cursor = 'pointer';
        avatarEl.title = 'Click to set/change avatar';
        avatarEl.addEventListener('click', () => openAvatarManager(userId, userName));
    }
    return div;
}

function createTerminalLogElement(data) {
    const div = document.createElement('div');
    div.className = `terminal-entry ${data.level || 'info'}`;
    const raw = data.message || data.content || '';
    const escaped = escapeHtml(raw);
    const q = (_filters.terminalQuery || '').trim();
    let highlighted = escaped;
    if (q) {
        try {
            const re = new RegExp(escapeRegExp(q), 'gi');
            highlighted = escaped.replace(re, (m) => `<mark class="hl">${m}</mark>`);
        } catch (err) {
            // If regex fails for any reason, fall back to no highlight
            highlighted = escaped;
        }
    }
    div.innerHTML = `
        <span class="terminal-timestamp">[${formatTimestamp(data.timestamp, true)}]</span>
        <span class="terminal-content">${highlighted}</span>
    `;
    return div;
}

function createLatencyLogElement(data) {
    const div = document.createElement('div');
    div.className = 'latency-entry';
    
    let metricsHtml = '';
    if (data.metrics && typeof data.metrics === 'object') {
        for (const [key, value] of Object.entries(data.metrics)) {
            const valueClass = getLatencyClass(value);
            metricsHtml += `
                <div class="latency-metric">
                    <span class="metric-label">${formatMetricLabel(key)}</span>
                    <span class="metric-value ${valueClass}">${formatMetricValue(value)}</span>
                </div>
            `;
        }
    } else {
        metricsHtml = `<div class="latency-metric">
            <span class="metric-label">Message</span>
            <span class="metric-value">${escapeHtml(data.message || data.content || 'No data')}</span>
        </div>`;
    }
    
    div.innerHTML = `
        <div class="log-header">
            <div class="log-title">
                <span class="log-type-badge latency">Performance</span>
                <span>Latency Report</span>
            </div>
            <div class="log-timestamp">${formatTimestamp(data.timestamp)}</div>
        </div>
        <div class="log-content">
            ${metricsHtml}
        </div>
    `;
    return div;
}

// Tab management
function switchTab(tabName) {
    currentTab = tabName;
    
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    // Scroll to bottom if auto-scroll is enabled
    if (autoScroll) {
        setTimeout(scrollToBottom, 100);
    }
}

// Utility functions
function formatTimestamp(timestamp, includeDate = false) {
    const date = new Date(timestamp);
    if (includeDate) {
        return date.toLocaleString();
    }
    return date.toLocaleTimeString();
}

function formatMetricLabel(key) {
    return key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function formatMetricValue(value) {
    if (typeof value === 'number') {
        if (value < 1) {
            return `${Math.round(value * 1000)}ms`;
        }
        return `${value.toFixed(2)}s`;
    }
    return String(value);
}

function getLatencyClass(value) {
    if (typeof value === 'number') {
        if (value > 3) return 'critical';
        if (value > 1) return 'warning';
    }
    return '';
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function escapeRegExp(text) {
    return text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function updateLogCounts() {
    document.getElementById('prompts-count').textContent = logs.prompts.length;
    document.getElementById('transcripts-count').textContent = logs.transcripts.length;
    document.getElementById('terminal-count').textContent = logs.terminal.length;
    document.getElementById('latency-count').textContent = logs.latency.length;
    const screenshotsCountEl = document.getElementById('screenshots-count');
    if (screenshotsCountEl) screenshotsCountEl.textContent = logs.screenshots.length;
    
    const total = Object.values(logs).reduce((sum, arr) => sum + arr.length, 0);
    document.getElementById('log-count').textContent = `${total} logs`;
}

async function updateConnectionStatus() {
    try {
        const stats = await ipcRenderer.invoke('get-log-stats');
        const statusEl = document.getElementById('connection-status');
        const dotEl = statusEl.querySelector('.status-dot');
        const textEl = statusEl.querySelector('span');
        
        if (stats.connected) {
            dotEl.className = 'status-dot connected';
            textEl.textContent = `Connected to Luna (${stats.clientCount} client${stats.clientCount !== 1 ? 's' : ''})`;
        } else {
            dotEl.className = 'status-dot disconnected';
            textEl.textContent = 'Waiting for Luna...';
        }
    } catch (error) {
        console.error('Error updating connection status:', error);
    }
}

function scrollToBottom() {
    // Scroll only the active tab container so header/tabs remain visible
    requestAnimationFrame(() => {
        const container = document.getElementById(`${currentTab}-container`);
        if (container) {
            container.scrollTo({ top: container.scrollHeight, behavior: 'smooth' });
        }
    });
}

function toggleAutoScroll() {
    autoScroll = !autoScroll;
    const button = document.getElementById('scroll-toggle');
    
    if (autoScroll) {
        button.classList.add('active');
        button.innerHTML = '<span>📍</span>';
        button.title = 'Auto-scroll (ON)';
        scrollToBottom();
    } else {
        button.classList.remove('active');
        button.innerHTML = '<span>⏸️</span>';
        button.title = 'Auto-scroll (OFF)';
    }
}

// Clear functions
function clearAllLogs() {
    if (confirm('Are you sure you want to clear all logs?')) {
        logs = { prompts: [], transcripts: [], terminal: [], latency: [], screenshots: [] };
        
        // Clear all containers
        ['prompts', 'transcripts', 'terminal', 'latency', 'screenshots'].forEach(tab => {
            const container = document.getElementById(`${tab}-container`);
            container.innerHTML = `
                <div class="empty-state">
                    <div class="empty-icon">${getTabIcon(tab)}</div>
                    <h3>No ${tab} logs yet</h3>
                    <p>${getTabDescription(tab)}</p>
                </div>
            `;
        });
        
        updateLogCounts();
    }
}

function clearTabLogs(tabType) {
    if (confirm(`Are you sure you want to clear all ${tabType} logs?`)) {
        logs[tabType] = [];
        
        const container = document.getElementById(`${tabType}-container`);
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">${getTabIcon(tabType)}</div>
                <h3>No ${tabType} logs yet</h3>
                <p>${getTabDescription(tabType)}</p>
            </div>
        `;
        
        updateLogCounts();
    }
}

function getTabIcon(tabType) {
    const icons = {
        prompts: '💭',
        transcripts: '🎤',
        terminal: '💻',
        latency: '⏱️',
        screenshots: '📸'
    };
    return icons[tabType] || '📝';
}

function getTabDescription(tabType) {
    const descriptions = {
        prompts: 'Prompt logs will appear here when Luna processes messages',
        transcripts: 'Voice transcripts will appear here when users speak in voice channels',
        terminal: 'Terminal output from main.py will appear here',
        latency: 'Performance metrics and latency reports will appear here'
    };
    return descriptions[tabType] || 'Logs will appear here';
}

// Export function
function exportLogs() {
    const data = {
        exported_at: new Date().toISOString(),
        total_logs: Object.values(logs).reduce((sum, arr) => sum + arr.length, 0),
        logs: logs
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `luna-logs-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    
    URL.revokeObjectURL(url);
    
    console.log('Logs exported successfully');
}

// Active filter state
const _filters = {
    prompts: 'all',
    terminal: 'all',
    transcriptsQuery: '',
    terminalQuery: ''
};

// Helpers for normalization
function _normalizeType(value) {
    return (value || '').toString().trim().toLowerCase().replace(/\s+/g, '-');
}
function _normalizeLevel(value) {
    const v = (value || '').toString().trim().toLowerCase();
    if (v === 'warn') return 'warning';
    return v;
}

function _renderTabItems(tabType, items) {
    const container = document.getElementById(`${tabType}-container`);
    if (!container) return;
    container.innerHTML = '';

    if (!items || items.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">${getTabIcon(tabType)}</div>
                <h3>No ${tabType} logs yet</h3>
                <p>${getTabDescription(tabType)}</p>
            </div>
        `;
        return;
    }

    const fragment = document.createDocumentFragment();
    items.forEach(data => {
        let el = null;
        if (tabType === 'prompts') el = createPromptLogElement(data);
        else if (tabType === 'transcripts') el = createTranscriptLogElement(data);
        else if (tabType === 'terminal') el = createTerminalLogElement(data);
        else if (tabType === 'latency') el = createLatencyLogElement(data);
        if (el) fragment.appendChild(el);
    });
    container.appendChild(fragment);

    if (autoScroll && tabType === currentTab) {
        scrollToBottom();
    }
}

function _getFilteredLogs(tabType) {
    switch (tabType) {
        case 'prompts': {
            const sel = _filters.prompts;
            if (sel === 'all') return logs.prompts;
            return logs.prompts.filter(p => _normalizeType(p.context_type) === sel);
        }
        case 'terminal': {
            const sel = _filters.terminal;
            let arr = logs.terminal;
            if (sel !== 'all') arr = arr.filter(t => _normalizeLevel(t.level) === sel);
            const q = (_filters.terminalQuery || '').trim().toLowerCase();
            if (!q) return arr;
            return arr.filter(t => {
                const msg = (t.message || t.content || '').toString().toLowerCase();
                return msg.includes(q);
            });
        }
        case 'transcripts': {
            const q = _filters.transcriptsQuery.trim().toLowerCase();
            if (!q) return logs.transcripts;
            return logs.transcripts.filter(t => {
                const user = (t.user || '').toString().toLowerCase();
                const msg = (t.message || t.content || '').toString().toLowerCase();
                return user.includes(q) || msg.includes(q);
            });
        }
        case 'latency':
            return logs.latency;
        default:
            return [];
    }
}

function applyActiveFilters(tabType) {
    const items = _getFilteredLogs(tabType);
    _renderTabItems(tabType, items);
}

// Filter and search functions
function filterLogs(tabType) {
    if (tabType === 'prompts') {
        const sel = document.getElementById('prompts-filter');
        _filters.prompts = _normalizeType(sel?.value || 'all');
    } else if (tabType === 'terminal') {
        const sel = document.getElementById('terminal-filter');
        _filters.terminal = _normalizeLevel(sel?.value || 'all');
    }
    applyActiveFilters(tabType);
}

function searchLogs(tabType) {
    if (tabType === 'transcripts') {
        const input = document.getElementById('transcripts-search');
        _filters.transcriptsQuery = (input?.value || '').toString();
        applyActiveFilters('transcripts');
    } else if (tabType === 'terminal') {
        const input = document.getElementById('terminal-search');
        _filters.terminalQuery = (input?.value || '').toString();
        applyActiveFilters('terminal');
    }
}

// Luna Control Functions
let lunaProcess = null;
let lunaStatus = 'stopped'; // 'starting', 'running', 'stopping', 'stopped'

function updateLunaControls() {
    const startBtn = document.getElementById('start-luna-btn');
    const stopBtn = document.getElementById('stop-luna-btn');
    
    switch (lunaStatus) {
        case 'starting':
            startBtn.disabled = true;
            startBtn.innerHTML = '<span>⏳</span> Starting...';
            stopBtn.disabled = true;
            break;
        case 'running':
            startBtn.disabled = true;
            stopBtn.disabled = false;
            stopBtn.innerHTML = '<span>⏹️</span> Stop Luna';
            break;
        case 'stopping':
            startBtn.disabled = true;
            stopBtn.disabled = true;
            stopBtn.innerHTML = '<span>⏳</span> Stopping...';
            break;
        case 'stopped':
        default:
            startBtn.disabled = false;
            startBtn.innerHTML = '<span>▶️</span> Start Luna';
            stopBtn.disabled = true;
            break;
    }
}

function startLuna() {
    if (lunaStatus !== 'stopped') return;
    
    lunaStatus = 'starting';
    updateLunaControls();
    
    // Send IPC message to main process to start Luna
    ipcRenderer.send('start-luna');
    
    // Update status after a delay
    setTimeout(() => {
        lunaStatus = 'running';
        updateLunaControls();
        updateConnectionStatus();
    }, 3000);
}

function stopLuna() {
    if (lunaStatus !== 'running') return;
    
    lunaStatus = 'stopping';
    updateLunaControls();
    
    // Send IPC message to main process to stop Luna
    ipcRenderer.send('stop-luna');
    
    // Update status after a delay
    setTimeout(() => {
        lunaStatus = 'stopped';
        updateLunaControls();
        updateConnectionStatus();
    }, 2000);
}

// Initialize Luna controls on startup
document.addEventListener('DOMContentLoaded', () => {
    updateLunaControls();
});
