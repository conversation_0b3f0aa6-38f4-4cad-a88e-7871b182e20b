#!/usr/bin/env python3
"""
Simple test for Luna's web search integration - no unicode characters
"""

import sys
import os

# Add the current directory to the path
sys.path.append('.')

def test_imports():
    """Test that all components can be imported"""
    print("Testing imports...")
    
    try:
        # Test web search module import
        from llm_response.rag_web_search import WebSearchRAG, perform_web_search, get_search_cache_stats
        print("SUCCESS: Web search module imported")
        
        # Test brain integration
        import llm_brain
        print("SUCCESS: Brain module imported")
        
        # Test configuration
        from llm_response.config import GEMMA_CPP_MODEL_PATH
        print("SUCCESS: Configuration imported")
        
        return True
        
    except Exception as e:
        print(f"FAILED: Import error - {e}")
        return False

def test_cache_system():
    """Test the caching system initialization"""
    print("\nTesting cache system...")
    
    try:
        from llm_response.rag_web_search import WebSearchRAG
        
        # Initialize web search system
        web_search = WebSearchRAG()
        print("SUCCESS: WebSearchRAG initialized")
        
        # Test cache directory creation
        if os.path.exists(web_search.cache_dir):
            print(f"SUCCESS: Cache directory exists - {web_search.cache_dir}")
        else:
            print(f"INFO: Cache directory will be created - {web_search.cache_dir}")
            
        # Test embedding model
        if web_search.embedding_model:
            print("SUCCESS: Sentence transformer model loaded")
        else:
            print("WARNING: Sentence transformer model not loaded - will use fallback")
            
        # Test FAISS index
        if web_search.faiss_index:
            print(f"SUCCESS: FAISS index initialized (entries: {web_search.faiss_index.ntotal})")
        else:
            print("WARNING: FAISS index not initialized")
            
        return True
        
    except Exception as e:
        print(f"FAILED: Cache system error - {e}")
        return False

def test_brain_patterns():
    """Test brain pattern detection"""
    print("\nTesting brain integration patterns...")
    
    # Test patterns that should trigger web search
    test_patterns = [
        "Luna search the web for news about AI",
        "Luna look up the weather in Tokyo", 
        "search for information about Python programming",
        "find me info about the latest GPU releases"
    ]
    
    for pattern in test_patterns:
        print(f"Pattern: '{pattern}'")
        
        # Simple pattern detection (would normally be done by brain)
        search_keywords = ["search", "look up", "find", "look for"]
        if any(keyword in pattern.lower() for keyword in search_keywords):
            print("  SUCCESS: Would trigger web search action")
        else:
            print("  WARNING: Would NOT trigger web search")
        print()

def test_google_credentials():
    """Test Google API credentials"""
    print("\nTesting Google API credentials...")
    
    from dotenv import load_dotenv
    load_dotenv('local.env')
    
    google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
    google_cx = os.getenv('GOOGLE_SEARCH_CX')
    
    if not google_api_key:
        print("WARNING: GOOGLE_SEARCH_API_KEY not found in local.env")
        return False
    else:
        print(f"SUCCESS: Google API Key configured (ends with: ...{google_api_key[-4:]})")
    
    if not google_cx:
        print("WARNING: GOOGLE_SEARCH_CX not found in local.env")
        return False
    else:
        print(f"SUCCESS: Google CX configured: {google_cx[:8]}...")
    
    return True

def main():
    print("Luna Web Search Integration Test")
    print("=" * 40)
    
    tests_passed = 0
    total_tests = 4
    
    # Run tests
    if test_imports():
        tests_passed += 1
    
    if test_cache_system():
        tests_passed += 1
    
    test_brain_patterns()  # Always passes, just informational
    tests_passed += 1
    
    if test_google_credentials():
        tests_passed += 1
    
    print("\n" + "=" * 40)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("SUCCESS: All tests passed!")
        print("Luna's web search system is ready to use!")
        print("\nTo test with actual web search:")
        print('1. Ensure Google API credentials are in local.env')
        print('2. Start Luna bot')
        print('3. Say: "Luna, search the web for latest AI news"')
    else:
        print("FAILED: Some tests failed. Check the output above for details.")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
