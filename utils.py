import re
import discord # Added for type hints and operations
import logging # Added for logging within the function
from llm_response.config import get_id_from_alias # Import alias helper


def remove_emojis(text):
    """
    Remove emojis from the provided text string.

    Args:
        text (str): The input text containing potential emojis.

    Returns:
        str: The text with emojis removed.
    """
    emoji_pattern = re.compile(
        "["
        u"\U0001F600-\U0001F64F"  # Emoticons
        u"\U0001F300-\U0001F5FF"  # Symbols & pictographs
        u"\U0001F680-\U0001F6FF"  # Transport & map symbols
        u"\U0001F1E0-\U0001F1FF"  # Flags
        u"\U00002700-\U000027BF"  # Dingbats
        u"\U0001F900-\U0001F9FF"  # Supplemental symbols and pictographs
        u"\U00002600-\U000026FF"  # Misc symbols
        u"\U00002B00-\U00002BFF"  # Arrows
        "]+", flags=re.UNICODE
    )
    return emoji_pattern.sub(r'', text)

import datetime

def format_datetime(timestamp: float, timezone_str: str = "America/Denver") -> str:
    """
    Formats a Unix timestamp into a human-readable string with a specified timezone.

    Args:
        timestamp (float): The Unix timestamp (seconds since epoch).
        timezone_str (str): The timezone name (e.g., 'America/Denver', 'UTC').

    Returns:
        str: Formatted date and time string (e.g., "Apr 03, 2025 03:24 PM").
             Returns "Invalid Timestamp" if input is not valid.
    """
    if timestamp is None or not isinstance(timestamp, (int, float)):
        return "Invalid Timestamp"
        
    try:
        # Use timezone-aware datetime objects
        dt_utc = datetime.datetime.fromtimestamp(timestamp, tz=datetime.timezone.utc)
        
        # Attempt to use pytz if available for robust timezone handling
        try:
            import pytz
            target_tz = pytz.timezone(timezone_str)
            dt_local = dt_utc.astimezone(target_tz)
        except ImportError:
            # Fallback if pytz is not installed (less reliable for DST)
            # Note: Python 3.9+ zoneinfo is better, but requires tzdata package
            # This basic offset calculation might be inaccurate for DST transitions
            offset_seconds = datetime.datetime.now().astimezone().utcoffset().total_seconds()
            target_tz = datetime.timezone(datetime.timedelta(seconds=offset_seconds))
            dt_local = dt_utc.astimezone(target_tz)
        except pytz.UnknownTimeZoneError:
             # Fallback to UTC if timezone string is invalid
             dt_local = dt_utc
             timezone_str = "UTC" # Indicate UTC is being used

        # Format the datetime object
        # Example format: "Apr 03, 2025 03:24 PM MST" (adjust format code as needed)
        return dt_local.strftime(f"%b %d, %Y %I:%M %p {dt_local.tzname()}")

    except (ValueError, OSError) as e:
        # Handle potential errors like invalid timestamp values
        print(f"Error formatting timestamp {timestamp}: {e}") # Use logger in real app
        return "Invalid Timestamp"


# --- User/Member Finding Helper (Moved from discord_sink.py) ---
# Setup logger for this module if not already present
logger_utils = logging.getLogger(__name__) # Use __name__ for module-specific logger

async def find_user_from_identifier(
    identifier: str,
    bot: discord.Client, # Pass bot instance for global fetch
    guild: discord.Guild | None # Pass guild for member search (optional)
) -> discord.User | discord.Member | None:
    """
    Helper to find a user globally (ID, Alias) or member in a specific guild (name/nick).

    Args:
        identifier: The string potentially containing a mention, ID, alias, name, or nickname.
        bot: The discord.Client or discord.Bot instance.
        guild: The discord.Guild to search within for names/nicks (optional).

    Returns:
        A discord.User or discord.Member object if found, otherwise None.
    """
    target_user = None
    user_id_to_fetch = None
    log_identifier = identifier # For logging clarity

    # 1. Try ID (mention or plain)
    mention_match = re.search(r"<@!?(\d+)>", identifier)
    plain_id_match = re.search(r"[\[\(]?(\d{17,})[\]\)]?", identifier) # Allow optional brackets/parens around ID
    if mention_match:
        try:
            user_id_to_fetch = int(mention_match.group(1))
            log_identifier = f"Mention ID {user_id_to_fetch}"
        except ValueError: pass
    elif plain_id_match:
        try:
            user_id_to_fetch = int(plain_id_match.group(1))
            log_identifier = f"Plain ID {user_id_to_fetch}"
        except ValueError: pass

    # 2. Try Alias Lookup if no ID extracted yet
    if not user_id_to_fetch:
        # Clean the identifier string for alias lookup
        alias_to_search = re.sub(r'[<@!>#()\[\]]', '', identifier).strip().lower()
        # Remove potential "User " prefix if present
        if alias_to_search.startswith("user "):
             alias_to_search = alias_to_search[5:].strip()

        if alias_to_search: # Only search if alias string is not empty
            logger_utils.debug(f"Attempting alias lookup for identifier: '{alias_to_search}'")
            found_id_from_alias = get_id_from_alias(alias_to_search) # Use imported helper
            if found_id_from_alias:
                user_id_to_fetch = found_id_from_alias
                log_identifier = f"Alias '{alias_to_search}' (ID: {user_id_to_fetch})"
                logger_utils.info(f"Found user ID {user_id_to_fetch} via alias '{alias_to_search}'.")
            else:
                 logger_utils.debug(f"Alias '{alias_to_search}' not found in USER_PROFILES.")
        else:
            logger_utils.warning(f"Cleaned identifier '{identifier}' resulted in empty string for alias lookup.")
            log_identifier = identifier # Revert log identifier if cleaning failed

    # 3. Fetch User by ID (if found via mention, plain ID, or alias)
    if user_id_to_fetch:
        try:
            logger_utils.debug(f"Attempting global fetch for user using final ID: {user_id_to_fetch}")
            target_user = await bot.fetch_user(user_id_to_fetch) # Global lookup using passed bot instance
        except (discord.NotFound, discord.HTTPException) as e:
            logger_utils.warning(f"Failed global fetch for user ID {user_id_to_fetch}: {e}")
            target_user = None # Ensure None on failure

    # 4. Fallback to name/nick in the provided guild (returns Member if found)
    if not target_user and guild: # Only proceed if guild context is provided
        logger_utils.debug(f"No user found via ID/Alias for '{log_identifier}'. Falling back to guild name/nick search in '{guild.name}'.")
        # Use the same cleaned name as used for alias lookup (or re-clean if needed)
        name_to_search_fallback = re.sub(r'[<@!>#()\[\]]', '', identifier).strip()
        if name_to_search_fallback.lower().startswith("user "):
            name_to_search_fallback = name_to_search_fallback[5:].strip()

        if name_to_search_fallback: # Proceed only if name is not empty
            log_identifier_fallback = f"Name/Nick '{name_to_search_fallback}' in Guild {guild.id}"
            try:
                logger_utils.debug(f"Attempting guild search for member by name/nick: '{name_to_search_fallback}'")
                # Iterate through cached members (requires Members Intent)
                found_in_guild = False
                # Check cache first
                for member in guild.members:
                    if member.name.lower() == name_to_search_fallback.lower() or \
                       (member.nick and member.nick.lower() == name_to_search_fallback.lower()):
                        target_user = member # Found member object
                        logger_utils.info(f"Found potential target by name/nick in guild cache (fallback): {target_user.display_name} ({target_user.id})")
                        log_identifier = log_identifier_fallback # Update final identifier used
                        found_in_guild = True
                        break
                # If not in cache, maybe try fetch_members? Requires intent and can be slow.
                # For now, we rely on the cache for name fallback.
                if not found_in_guild:
                    logger_utils.warning(f"Fallback search: Member matching name/nick '{name_to_search_fallback}' not found in guild cache.")
                    target_user = None # Ensure target_user is None if not found by fallback
            except Exception as e:
                logger_utils.error(f"Error during member iteration for fallback '{name_to_search_fallback}': {e}")
                target_user = None
        else:
            logger_utils.warning(f"Cleaned identifier '{identifier}' resulted in empty string for fallback name search.")
            # log_identifier remains as it was before fallback attempt
    elif not target_user and not guild:
         logger_utils.debug("No guild provided for name/nick fallback search.")

    if not target_user:
         logger_utils.warning(f"Could not find any user/member matching '{log_identifier}' after all checks.")

    return target_user