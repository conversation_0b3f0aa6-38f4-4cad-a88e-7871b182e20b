"""Pokemon Type Effectiveness RAG for Luna"""

import csv
import os
import re
from typing import Dict, Optional, Tuple

_TYPE_DATA = {}
_DATA_LOADED = False
_DATASET_PATH = os.path.join(os.path.dirname(__file__), "datasets", "type_chart.csv")

_POKEMON_TYPES = [
    "normal", "fire", "water", "electric", "grass", "ice", "fighting", 
    "poison", "ground", "flying", "psychic", "bug", "rock", "ghost", 
    "dragon", "dark", "steel", "fairy"
]

_TYPE_PATTERNS = [
    r"what.*(?:beats?|counters?|effective against)\s+(\w+)",
    r"what.*(?:is|are)\s+(\w+).*(?:weak|vulnerable).*to",
    r"(\w+).*(?:weakness|weak to)",
    r"(\w+).*(?:strength|good against)"
]

def _load_dataset():
    global _DATA_LOADED
    if _DATA_LOADED:
        return
    
    with open(_DATASET_PATH, newline="", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            attacking = row["attacking_type"].strip().lower()
            defending = row["defending_type"].strip().lower()
            effectiveness = float(row["effectiveness_multiplier"])
            
            if attacking not in _TYPE_DATA:
                _TYPE_DATA[attacking] = {}
            _TYPE_DATA[attacking][defending] = effectiveness
    
    _DATA_LOADED = True

def needs_type_effectiveness_context(question: str) -> Tuple[bool, Optional[str]]:
    if not question:
        return False, None
    
    for pattern in _TYPE_PATTERNS:
        match = re.search(pattern, question, re.I)
        if match:
            type_name = match.group(1).lower()
            if type_name in _POKEMON_TYPES:
                return True, type_name
    
    return False, None

def get_type_effectiveness_context(type_name: str, question: str) -> str:
    _load_dataset()
    
    type_name = type_name.lower()
    if type_name not in _POKEMON_TYPES:
        return ""
    
    # Check if asking about weaknesses
    is_weakness = any(word in question.lower() for word in ["weak", "vulnerable", "beats", "counters"])
    
    if is_weakness:
        weaknesses = []
        for attacking_type, defending_types in _TYPE_DATA.items():
            if type_name in defending_types and defending_types[type_name] == 2.0:
                weaknesses.append(attacking_type.capitalize())
        
        if weaknesses:
            return f"{type_name.capitalize()} types are weak to {', '.join(weaknesses)} types."
    else:
        # Find strengths
        strengths = []
        if type_name in _TYPE_DATA:
            for defending_type, multiplier in _TYPE_DATA[type_name].items():
                if multiplier == 2.0:
                    strengths.append(defending_type.capitalize())
        
        if strengths:
            return f"{type_name.capitalize()} types are super effective against {', '.join(strengths)} types."
    
    return f"{type_name.capitalize()} type information available."
