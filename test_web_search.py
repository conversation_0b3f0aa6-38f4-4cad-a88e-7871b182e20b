#!/usr/bin/env python3
"""
Test script for <PERSON>'s web search RAG functionality.
Tests the integration of Google Custom Search API with vector caching and LLM summarization.
"""

import asyncio
import os
import sys
sys.path.append('.')

from llm_response.rag_web_search import WebSearchRAG, perform_web_search, get_search_cache_stats
from dotenv import load_dotenv

async def test_web_search():
    """Test the web search functionality"""
    print("Testing Luna's Web Search RAG System")
    print("=" * 50)
    
    # Load environment variables
    load_dotenv('local.env')
    
    # Check if Google API credentials are configured
    google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
    google_cx = os.getenv('GOOGLE_SEARCH_CX')
    
    if not google_api_key or not google_cx:
        print("ERROR: Google Search API credentials not configured!")
        print("Please set GOOGLE_SEARCH_API_KEY and GOOGLE_SEARCH_CX in local.env")
        return
    
    print(f"Google API Key: {'*' * (len(google_api_key) - 4)}{google_api_key[-4:]}")
    print(f"Google Search CX: {google_cx[:8]}...")
    print()
    
    # Test queries
    test_queries = [
        "latest AI news 2024",
        "Python programming tips",
        "weather in Tokyo today",
        "GPU releases 2024"
    ]
    
    print("🧪 Testing Web Search Queries:")
    print("-" * 30)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n{i}. Testing query: '{query}'")
        print("-" * (len(query) + 20))
        
        try:
            # Test the async search function
            result = await perform_web_search(query)
            
            if result:
                print(f"✅ Search successful!")
                print(f"Summary: {result[:200]}..." if len(result) > 200 else f"Summary: {result}")
            else:
                print("❌ Search returned no results")
                
        except Exception as e:
            print(f"❌ Search failed: {e}")
    
    print("\n📊 Cache Statistics:")
    print("-" * 20)
    try:
        stats = get_search_cache_stats()
        for key, value in stats.items():
            print(f"{key}: {value}")
    except Exception as e:
        print(f"❌ Failed to get cache stats: {e}")

async def test_brain_integration():
    """Test brain integration patterns"""
    print("\n🧠 Testing Brain Integration Patterns:")
    print("-" * 40)
    
    # Test patterns that should trigger web search
    test_patterns = [
        "Luna search the web for news about AI",
        "Luna look up the weather in Tokyo", 
        "search for information about Python programming",
        "find me info about the latest GPU releases"
    ]
    
    for pattern in test_patterns:
        print(f"Pattern: '{pattern}'")
        # This would normally be processed by the brain system
        # For now, just show that the pattern would be recognized
        if "search" in pattern.lower() or "look up" in pattern.lower() or "find" in pattern.lower():
            print("✅ Would trigger web search action")
        else:
            print("❌ Would NOT trigger web search")
        print()

def test_cache_system():
    """Test the caching system"""
    print("\n💾 Testing Cache System:")
    print("-" * 25)
    
    try:
        # Initialize web search system
        web_search = WebSearchRAG()
        print("✅ WebSearchRAG initialized successfully")
        
        # Test cache directory creation
        if os.path.exists(web_search.cache_dir):
            print(f"✅ Cache directory exists: {web_search.cache_dir}")
        else:
            print(f"❌ Cache directory missing: {web_search.cache_dir}")
            
        # Test embedding model
        if web_search.embedding_model:
            print("✅ Sentence transformer model loaded")
            
            # Test embedding generation
            test_embedding = web_search._get_query_embedding("test query")
            if test_embedding is not None:
                print(f"✅ Embedding generation works (shape: {test_embedding.shape})")
            else:
                print("❌ Embedding generation failed")
        else:
            print("❌ Sentence transformer model not loaded")
            
        # Test FAISS index
        if web_search.faiss_index:
            print(f"✅ FAISS index initialized (entries: {web_search.faiss_index.ntotal})")
        else:
            print("❌ FAISS index not initialized")
            
    except Exception as e:
        print(f"❌ Cache system test failed: {e}")

if __name__ == "__main__":
    print("Luna Web Search Test Suite")
    print("=" * 40)
    
    # Test cache system first (no API calls needed)
    test_cache_system()
    
    # Test brain integration patterns
    asyncio.run(test_brain_integration())
    
    # Test actual web search (requires API credentials)
    print("\nWeb Search API Tests:")
    print("Note: These tests require Google Search API credentials")
    proceed = input("Proceed with API tests? (y/N): ").lower().strip()
    
    if proceed == 'y':
        asyncio.run(test_web_search())
    else:
        print("Skipping API tests.")
    
    print("\nTest suite completed!")
    print("If all tests passed, Luna's web search system is ready!")
