@echo off
title Luna Control Center
echo ========================================
echo        LUNA CONTROL CENTER  
echo ========================================
echo.

REM Change to script directory
cd /d "%~dp0"

echo [1/2] Starting Luna Control Center App...
start "Luna Control Center" cmd /c "npm start"
echo Electron app is starting...
timeout /t 3 /nobreak >nul

echo [2/2] Starting Luna Discord Bot...
echo.

REM Luna is controlled by the Electron app buttons, not launched automatically
REM The Electron app will handle starting/stopping Luna via IPC commands

echo.
echo Luna has stopped.
pause
