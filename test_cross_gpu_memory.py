#!/usr/bin/env python3
"""
Focused test for cross-GPU memory isolation issue.
This test specifically reproduces the access violation that occurs when 
CUDA (NVIDIA) and Vulkan (AMD) contexts are used simultaneously.
"""

import time
import threading
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_sequential_cross_gpu_usage():
    """Test using both GPUs sequentially - should work fine"""
    logger.info("=== Testing Sequential Cross-GPU Usage ===")
    
    try:
        from llm_response.initialization import get_gemma3_client, get_qwen3_client
        from shared_model import call_model_safe
        
        nvidia_model = get_gemma3_client()
        amd_model = get_qwen3_client()
        
        # Test NVIDIA first
        logger.info("Testing NVIDIA GPU first...")
        nvidia_result = call_model_safe(
            model=nvidia_model,
            prompt="Sequential test 1: What is 1+1?",
            gpu_target="nvidia_4070",
            max_tokens=20,
            temperature=0.1
        )
        logger.info("NVIDIA GPU sequential test: SUCCESS")
        
        # Brief delay to ensure context cleanup
        time.sleep(0.5)
        
        # Test AMD second
        logger.info("Testing AMD GPU second...")
        amd_result = call_model_safe(
            model=amd_model,
            prompt="Sequential test 2: What is 2+2?",
            gpu_target="amd_6650xt",
            max_tokens=20,
            temperature=0.1
        )
        logger.info("AMD GPU sequential test: SUCCESS")
        
        return True
        
    except Exception as e:
        logger.error(f"Sequential test failed: {e}")
        return False

def test_concurrent_cross_gpu_usage():
    """Test using both GPUs concurrently - reproduces access violation"""
    logger.info("=== Testing Concurrent Cross-GPU Usage ===")
    
    try:
        from llm_response.initialization import get_gemma3_client, get_qwen3_client
        from shared_model import call_model_safe
        
        nvidia_model = get_gemma3_client()
        amd_model = get_qwen3_client()
        
        def nvidia_task():
            """NVIDIA GPU task"""
            logger.info("Starting concurrent NVIDIA task...")
            return call_model_safe(
                model=nvidia_model,
                prompt="Concurrent test: What is 3+3?",
                gpu_target="nvidia_4070",
                max_tokens=20,
                temperature=0.1
            )
        
        def amd_task():
            """AMD GPU task"""
            logger.info("Starting concurrent AMD task...")
            return call_model_safe(
                model=amd_model,
                prompt="Concurrent test: What is 4+4?",
                gpu_target="amd_6650xt",
                max_tokens=20,
                temperature=0.1
            )
        
        # Launch both tasks simultaneously
        with ThreadPoolExecutor(max_workers=2) as executor:
            nvidia_future = executor.submit(nvidia_task)
            amd_future = executor.submit(amd_task)
            
            results = []
            for future in as_completed([nvidia_future, amd_future], timeout=30):
                try:
                    result = future.result()
                    results.append(("SUCCESS", result))
                except Exception as e:
                    results.append(("FAILED", str(e)))
            
        return results
        
    except Exception as e:
        logger.error(f"Concurrent test setup failed: {e}")
        return [("SETUP_FAILED", str(e))]

def test_memory_context_isolation():
    """Test to investigate memory context separation issues"""
    logger.info("=== Testing Memory Context Isolation ===")
    
    try:
        from llm_response.initialization import get_gemma3_client, get_qwen3_client
        from shared_model import call_model_safe
        import gc
        import os
        
        # Check environment variables
        logger.info("Environment Check:")
        logger.info(f"  GGML_BACKEND: {os.environ.get('GGML_BACKEND', 'Not set')}")
        logger.info(f"  CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES', 'Not set')}")
        logger.info(f"  GGML_VK_VISIBLE_DEVICES: {os.environ.get('GGML_VK_VISIBLE_DEVICES', 'Not set')}")
        
        # Initialize models separately with explicit cleanup
        logger.info("Initializing NVIDIA model...")
        nvidia_model = get_gemma3_client()
        
        logger.info("Forcing garbage collection...")
        gc.collect()
        
        logger.info("Initializing AMD model...")
        amd_model = get_qwen3_client()
        
        logger.info("Forcing garbage collection again...")
        gc.collect()
        
        # Test with explicit context switching delays
        def safe_nvidia_call():
            """Safe NVIDIA call with cleanup"""
            try:
                result = call_model_safe(
                    model=nvidia_model,
                    prompt="Context test: Simple math 5+5?",
                    gpu_target="nvidia_4070",
                    max_tokens=15,
                    temperature=0.1
                )
                gc.collect()  # Force cleanup after call
                return result
            except Exception as e:
                logger.error(f"NVIDIA context call failed: {e}")
                gc.collect()
                raise
        
        def safe_amd_call():
            """Safe AMD call with cleanup"""
            try:
                result = call_model_safe(
                    model=amd_model,
                    prompt="Context test: Simple math 6+6?",
                    gpu_target="amd_6650xt",
                    max_tokens=15,
                    temperature=0.1
                )
                gc.collect()  # Force cleanup after call
                return result
            except Exception as e:
                logger.error(f"AMD context call failed: {e}")
                gc.collect()
                raise
        
        # Test rapid context switching
        logger.info("Testing rapid context switching...")
        for i in range(3):
            logger.info(f"  Switch cycle {i+1}")
            try:
                # NVIDIA -> AMD -> NVIDIA with minimal delay
                safe_nvidia_call()
                time.sleep(0.1)  # Minimal delay
                safe_amd_call()
                time.sleep(0.1)
                safe_nvidia_call()
                logger.info(f"  Switch cycle {i+1}: SUCCESS")
            except Exception as e:
                logger.error(f"  Switch cycle {i+1}: FAILED - {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"Memory context test failed: {e}")
        return False

def main():
    """Run all cross-GPU memory isolation tests"""
    print("=" * 80)
    print("CROSS-GPU MEMORY ISOLATION TESTS")
    print("=" * 80)
    
    # Test 1: Sequential usage (baseline)
    print("\n[TEST 1] Sequential Cross-GPU Usage")
    print("-" * 50)
    sequential_result = test_sequential_cross_gpu_usage()
    print(f"Result: {'PASS' if sequential_result else 'FAIL'}")
    
    # Test 2: Concurrent usage (reproduces issue)
    print("\n[TEST 2] Concurrent Cross-GPU Usage")
    print("-" * 50)
    concurrent_results = test_concurrent_cross_gpu_usage()
    for status, detail in concurrent_results:
        print(f"  {status}: {detail}")
    
    # Test 3: Memory context isolation
    print("\n[TEST 3] Memory Context Isolation")
    print("-" * 50)
    context_result = test_memory_context_isolation()
    print(f"Result: {'PASS' if context_result else 'FAIL'}")
    
    print("\n" + "=" * 80)
    print("ANALYSIS")
    print("=" * 80)
    print("Sequential usage should work fine (CUDA and Vulkan used separately)")
    print("Concurrent usage likely fails due to memory context conflicts")
    print("Memory context test helps identify the root cause of isolation issues")

if __name__ == "__main__":
    main()
