@echo off
title Luna Control Center - Debug Mode
echo ========================================
echo    LUNA CONTROL CENTER - DEBUG MODE    
echo ========================================
echo.

REM Enable command echoing for debug
echo DEBUG: Script starting...
echo DEBUG: Current directory: %cd%
echo DEBUG: Script directory: %~dp0

REM Set the script directory
set SCRIPT_DIR=%~dp0
echo DEBUG: Changing to script directory: %SCRIPT_DIR%
cd /d "%SCRIPT_DIR%"
echo DEBUG: New current directory: %cd%

echo.
echo ===== SYSTEM CHECKS =====
echo.

echo Checking Node.js installation...
node --version
if errorlevel 1 (
    echo ERROR: Node.js not found
    echo Please install Node.js from https://nodejs.org
    pause
    exit /b 1
) else (
    echo ✓ Node.js found
)

echo.
echo Checking npm installation...
npm --version
if errorlevel 1 (
    echo ERROR: npm not found
    pause
    exit /b 1
) else (
    echo ✓ npm found
)

echo.
echo Checking for package.json...
if exist "package.json" (
    echo ✓ package.json found
) else (
    echo ERROR: package.json not found in %cd%
    dir
    pause
    exit /b 1
)

echo.
echo ===== INSTALLATION =====
echo.

echo Installing dependencies (this may take a moment)...
npm install
if errorlevel 1 (
    echo ERROR: npm install failed
    echo Check the error messages above
    pause
    exit /b 1
) else (
    echo ✓ Dependencies installed successfully
)

echo.
echo ===== STARTING ELECTRON APP =====
echo.

echo Starting Luna Control Center...
echo DEBUG: Running 'npm start'
start "Luna Control Center" cmd /k "npm start"

echo.
echo ✓ Luna Control Center should be starting now
echo.
echo This debug window will stay open for troubleshooting.
echo Press any key to exit when done...
pause >nul
