#!/usr/bin/env python3
"""
Direct test of GPU allocation fix without importing complex modules
"""

import os
import sys
import logging
from load_environment import load_env_vars

# Load environment variables first
load_env_vars()

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_amd_gpu_direct():
    """Test AMD GPU allocation directly"""
    logger.info("=" * 60)
    logger.info("TESTING AMD GPU ALLOCATION (DIRECT)")
    logger.info("=" * 60)
    
    try:
        from llama_cpp import Llama
        
        # Set environment to only show AMD GPU (like our fix does)
        os.environ["GGML_BACKEND"] = "vulkan"
        os.environ["CUDA_VISIBLE_DEVICES"] = ""
        os.environ["GGML_CUDA"] = "0"
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "1"  # Only AMD GPU
        
        logger.info("Environment settings:")
        logger.info(f"GGML_BACKEND: {os.environ.get('GGML_BACKEND')}")
        logger.info(f"GGML_VK_VISIBLE_DEVICES: {os.environ.get('GGML_VK_VISIBLE_DEVICES')}")
        logger.info(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES')}")
        
        logger.info("Loading model on AMD GPU...")
        model = Llama(
            model_path="C:/Users/<USER>/.lmstudio/models/lmstudio-community/gemma-3-4B-it-qat-GGUF/gemma-3-4B-it-QAT-Q4_0.gguf",
            n_ctx=2048,
            n_gpu_layers=-1,
            main_gpu=0,  # Should be AMD GPU since it's the only visible one
            verbose=True
        )
        
        logger.info("✅ Model loaded successfully")
        
        # Test generation
        response = model("Hello", max_tokens=3, temperature=0.1)
        logger.info(f"Test response: {response['choices'][0]['text']}")
        
        # Clean up
        del model
        
        # Reset environment
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "0,1"
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_nvidia_gpu_direct():
    """Test NVIDIA GPU allocation directly"""
    logger.info("=" * 60)
    logger.info("TESTING NVIDIA GPU ALLOCATION (DIRECT)")
    logger.info("=" * 60)
    
    try:
        from llama_cpp import Llama
        
        # Set environment to only show NVIDIA GPU
        os.environ["GGML_BACKEND"] = "vulkan"
        os.environ["CUDA_VISIBLE_DEVICES"] = ""
        os.environ["GGML_CUDA"] = "0"
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "0"  # Only NVIDIA GPU
        
        logger.info("Environment settings:")
        logger.info(f"GGML_BACKEND: {os.environ.get('GGML_BACKEND')}")
        logger.info(f"GGML_VK_VISIBLE_DEVICES: {os.environ.get('GGML_VK_VISIBLE_DEVICES')}")
        logger.info(f"CUDA_VISIBLE_DEVICES: {os.environ.get('CUDA_VISIBLE_DEVICES')}")
        
        logger.info("Loading model on NVIDIA GPU...")
        model = Llama(
            model_path="./models/luna-model.gguf",
            n_ctx=2048,
            n_gpu_layers=35,
            main_gpu=0,  # Should be NVIDIA GPU since it's the only visible one
            verbose=True
        )
        
        logger.info("✅ Model loaded successfully")
        
        # Test generation
        response = model("Hello", max_tokens=3, temperature=0.1)
        logger.info(f"Test response: {response['choices'][0]['text']}")
        
        # Clean up
        del model
        
        # Reset environment
        os.environ["GGML_VK_VISIBLE_DEVICES"] = "0,1"
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    logger.info("Starting direct GPU allocation test")
    logger.info("This test simulates the fix by manipulating GGML_VK_VISIBLE_DEVICES")
    logger.info("=" * 60)
    
    # Test AMD GPU
    amd_success = test_amd_gpu_direct()
    
    # Test NVIDIA GPU
    nvidia_success = test_nvidia_gpu_direct()
    
    logger.info("=" * 60)
    logger.info("DIRECT GPU ALLOCATION TEST COMPLETE")
    logger.info("=" * 60)
    
    if amd_success:
        logger.info("✅ AMD GPU test passed - check logs for 'AMD Radeon RX 6650 XT'")
    else:
        logger.info("❌ AMD GPU test failed")
        
    if nvidia_success:
        logger.info("✅ NVIDIA GPU test passed - check logs for 'NVIDIA GeForce RTX 4070'")
    else:
        logger.info("❌ NVIDIA GPU test failed")
        
    logger.info("=" * 60)
    logger.info("KEY FINDINGS:")
    logger.info("- Look for 'ggml_vulkan: Found X Vulkan devices' in the output")
    logger.info("- Look for 'llama_model_load_from_file_impl: using device VulkanX'")
    logger.info("- Look for 'load_tensors: layer X assigned to device VulkanX'")
    logger.info("If the fix works, you should see different GPUs being used in each test!")

if __name__ == "__main__":
    main()
