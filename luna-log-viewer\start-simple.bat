@echo off
title Luna Control Center - Simple Start
echo ========================================
echo      LUNA CONTROL CENTER - SIMPLE     
echo ========================================
echo.

REM Get the script directory
set SCRIPT_DIR=%~dp0
cd /d "%SCRIPT_DIR%"

echo Step 1: Installing Electron dependencies...
npm install --silent
if errorlevel 1 (
    echo ERROR: Failed to install npm dependencies
    echo Make sure Node.js is installed
    pause
    exit /b 1
)

echo.
echo Step 2: Starting Luna Log Viewer...
echo The Electron app will start in a new window...
start "Luna Log Viewer" cmd /c "npm start"

echo.
echo Step 3: Luna Control Center is ready!
echo.
echo The Luna Control Center window should open automatically.
echo Use the Start/Stop buttons in the interface to control Luna.
echo.
echo Press any key to close this setup window...
pause >nul

echo.
echo Setup complete! You can now close this window.
echo Use the Luna Control Center interface to manage Luna.
