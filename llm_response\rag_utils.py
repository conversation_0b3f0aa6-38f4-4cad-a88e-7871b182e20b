import logging
import time

# Import necessary components from other modules within the package
from .config import R<PERSON><PERSON><PERSON><PERSON>_MODEL, RE<PERSON>ITE_TEMP, REWRITE_MAX_TOKENS
from shared_model import call_model_safe
from llm_brain import get_gemma_cpp_client
from .config import GEMMA_CPP_MODEL_PATH, LLAMA_CPP_N_CTX, LLAMA_CPP_N_THREADS, LLAMA_CPP_N_GPU_LAYERS, LLAMA_CPP_USE_MMAP, LLAMA_CPP_USE_MLOCK, LLAMA_CPP_VERBOSE
# Removed safe_model_call import - using direct client calls instead

logger = logging.getLogger(__name__)

# --- Conversation Memory RAG (FTS5 over conversation_logs.db) ---
import sqlite3
import math
import asyncio
import re
from typing import List, Dict, Any, Tuple, Optional

from .config import (
	DB_PATH,
	RAG_ENABLE_QUERY_REWRITE,
	RAG_FTS_MAX_CANDIDATES,
	RAG_TOP_K,
	RAG_RECENCY_HALFLIFE_DAYS,
	RAG_REQUIRE_SAME_CHANNEL,
	RAG_RESPECT_DM_SCOPE,
	RAG_MAX_CONTEXT_CHARS,
	RAG_MIN_CONTEXT_CHARS,
	RAG_MIN_SCORE_THRESHOLD,
	get_main_name_by_id,
)

_FTS_READY = False
_FTS_LOCK = asyncio.Lock()

def _tokenize(text: str) -> List[str]:
	if not text:
		return []
	# Lowercase and keep alphanumerics only (strip apostrophes to avoid FTS MATCH parse issues)
	return re.findall(r"[a-z0-9]+", text.lower())

def _time_decay_weight(now_ts: float, past_ts: float, halflife_days: float) -> float:
	if not past_ts:
		return 1.0
	age_days = max(0.0, (now_ts - past_ts) / 86400.0)
	if halflife_days <= 0:
		return 1.0
	return 0.5 ** (age_days / halflife_days)

def _safe_connect() -> sqlite3.Connection:
	conn = sqlite3.connect(DB_PATH, timeout=15)
	conn.execute("PRAGMA journal_mode=WAL;")
	conn.execute("PRAGMA synchronous=NORMAL;")
	return conn

def _fetch_next_assistant_reply(after_id: int, channel_id: Optional[str]) -> Optional[str]:
	"""Fetch the next assistant reply after a given interaction id within the same channel.

	Returns the assistant content or None if not found.
	"""
	if channel_id is None:
		return None
	conn = None
	try:
		conn = _safe_connect()
		cur = conn.cursor()
		cur.execute(
			"""
			SELECT content
			FROM interactions
			WHERE id > ? AND channel_id = ? AND role = 'assistant'
			ORDER BY id ASC
			LIMIT 1
			""",
			(after_id, str(channel_id))
		)
		row = cur.fetchone()
		return (row[0] or "").strip() if row else None
	except Exception:
		return None
	finally:
		if conn:
			conn.close()

def _ensure_fts_schema_sync() -> None:
	conn = None
	try:
		conn = _safe_connect()
		cur = conn.cursor()
		# Ensure base interactions table exists (minimal schema)
		cur.execute(
			"""
			CREATE TABLE IF NOT EXISTS interactions (
				id INTEGER PRIMARY KEY AUTOINCREMENT,
				user_id TEXT,
				role TEXT,
				content TEXT,
				timestamp REAL,
				audio_start_time REAL,
				channel_type TEXT,
				channel_id TEXT
			);
			"""
		)
		# Create FTS5 index linked to interactions table
		cur.execute(
			"""
			CREATE VIRTUAL TABLE IF NOT EXISTS interactions_fts USING fts5(
				content,
				role,
				user_id,
				channel_type,
				channel_id,
				timestamp UNINDEXED,
				content='interactions',
				content_rowid='id',
				tokenize = 'porter unicode61'
			);
			"""
		)
		# Triggers to keep FTS in sync
		cur.execute(
			"""
			CREATE TRIGGER IF NOT EXISTS interactions_ai AFTER INSERT ON interactions BEGIN
				INSERT INTO interactions_fts(rowid, content, role, user_id, channel_type, channel_id, timestamp)
				VALUES (new.id, new.content, new.role, new.user_id, new.channel_type, new.channel_id, new.timestamp);
			END;
			"""
		)
		cur.execute(
			"""
			CREATE TRIGGER IF NOT EXISTS interactions_ad AFTER DELETE ON interactions BEGIN
				DELETE FROM interactions_fts WHERE rowid = old.id;
			END;
			"""
		)
		cur.execute(
			"""
			CREATE TRIGGER IF NOT EXISTS interactions_au AFTER UPDATE ON interactions BEGIN
				DELETE FROM interactions_fts WHERE rowid = old.id;
				INSERT INTO interactions_fts(rowid, content, role, user_id, channel_type, channel_id, timestamp)
				VALUES (new.id, new.content, new.role, new.user_id, new.channel_type, new.channel_id, new.timestamp);
			END;
			"""
		)
		# Rebuild the FTS index to ensure it reflects current data
		try:
			cur.execute("INSERT INTO interactions_fts(interactions_fts) VALUES('rebuild');")
		except sqlite3.Error:
			# If rebuild is not supported in this build, fallback to manual sync
			cur.execute("DELETE FROM interactions_fts;")
			cur.execute(
				"""
				INSERT INTO interactions_fts(rowid, content, role, user_id, channel_type, channel_id, timestamp)
				SELECT id, content, role, user_id, channel_type, channel_id, timestamp FROM interactions;
				"""
			)
		conn.commit()
	finally:
		if conn:
			conn.close()

async def initialize_conversation_memory() -> bool:
	"""Ensure the FTS5 memory index exists and is synchronized."""
	global _FTS_READY
	async with _FTS_LOCK:
		if _FTS_READY:
			return True
		loop = asyncio.get_event_loop()
		await loop.run_in_executor(None, _ensure_fts_schema_sync)
		_FTS_READY = True
		logger.info("✅ Conversation memory FTS initialized and synced")
		return True

def _build_fts_query(raw_query: str) -> str:
	# Simple token OR query with prefix matching to improve recall
	tokens = _tokenize(raw_query)
	if not tokens:
		return ''
	# Use prefix operator for each token to catch morphological variants
	parts = [f"{t}*" for t in tokens if len(t) > 1]
	if not parts:
		parts = tokens
	# Intent-aware expansions (helps when rewrite is disabled)
	low = raw_query.lower()
	if any(k in low for k in ["favorite", "favourite", "prefer", "preference", "like", "love"]):
		parts.extend(["favorite*", "prefer*", "preference*", "like*", "love*"])
	if any(k in low for k in ["luna", "your", "you", "ur", "u"]):
		parts.extend(["luna*", "i*", "my*"])
	if "potato" in low:
		parts.extend(["potato*", "potatoes*"])
	# Join with OR; wrap in quotes to reduce syntax errors; escape embedded quotes
	query_str = ' OR '.join(parts)
	# FTS5 MATCH is not standard SQL LIKE; avoid bare quotes in content
	query_str = query_str.replace('"', ' ')
	return query_str

def _score_candidate(query_tokens: List[str], doc_text: str, role: str, same_channel: bool, same_user: bool, now_ts: float, msg_ts: float) -> float:
	doc_tokens = _tokenize(doc_text)
	if not doc_tokens:
		return 0.0
	set_q, set_d = set(query_tokens), set(doc_tokens)
	if not set_q:
		return 0.0
	# Jaccard similarity as lexical relevance
	intersection = len(set_q & set_d)
	union = len(set_q | set_d)
	lex_sim = (intersection / union) if union else 0.0
	# Density boost for multiple overlaps
	density = min(1.0, intersection / max(1.0, len(set_q)))
	# Time decay
	recency = _time_decay_weight(now_ts, msg_ts, RAG_RECENCY_HALFLIFE_DAYS)
	# Intent-aware boosts
	is_about_assistant = any(t in {"luna", "your", "you", "ur", "u"} for t in set_q)
	preference_intent = any(t in {"favorite", "favourite", "prefer", "preference", "like", "love"} for t in set_q)
	doc_lower = doc_text.lower()
	has_pref_words = bool(re.search(r"\b(favorite|favourite|prefer|preference|like|love)\b", doc_lower))
	has_potato = ("potato" in doc_lower) or ("potatoes" in doc_lower)
	doc_first_person = bool(re.search(r"\b(i|i'm|im|my|mine)\b", doc_lower))
	# Role weighting (favor assistant when query is about Luna/self)
	role_w = 1.0
	if role == 'assistant' and is_about_assistant:
		role_w = 1.2
	elif role == 'user' and is_about_assistant:
		role_w = 0.95
	# Reduce recency penalty for stable preferences
	if preference_intent:
		recency = max(recency, 0.9)
	# Intent boost combining signals
	intent_boost = 1.0
	if is_about_assistant and role == 'assistant':
		intent_boost *= 1.3
	if preference_intent and has_pref_words:
		intent_boost *= 1.2
	if preference_intent and doc_first_person and role == 'assistant':
		intent_boost *= 1.1
	if ("potato" in set_q or "potatoes" in set_q) and has_potato:
		intent_boost *= 1.2
	# Channel/user boosts
	# IMPORTANT: For stable preference questions, do NOT bias toward same channel,
	# otherwise text vs voice may diverge if each channel has different historical claims.
	if preference_intent:
		chan_w = 1.0
	else:
		chan_w = 1.15 if same_channel else 1.0
	user_w = 1.1 if same_user else 1.0
	return (0.7 * lex_sim + 0.3 * density) * recency * role_w * chan_w * user_w * intent_boost

def _fts_search_sync(fts_query: str, limit: int, channel_id: Optional[str], is_dm: bool, current_channel_only: bool, current_channel_id: Optional[str], target_user_id: Optional[int] = None, target_role: Optional[str] = None) -> List[Dict[str, Any]]:
	conn = None
	try:
		conn = _safe_connect()
		cur = conn.cursor()
		params: List[Any] = []
		where_clauses: List[str] = []
		if fts_query:
			where_clauses.append("interactions_fts MATCH ?")
			params.append(fts_query)
		# User/Role filtering for precise targeting
		if target_user_id is not None:
			where_clauses.append("user_id = ?")
			params.append(str(target_user_id))
		if target_role is not None:
			where_clauses.append("role = ?")
			params.append(target_role)
		
		# Scope handling
		if current_channel_only and current_channel_id is not None:
			where_clauses.append("channel_id = ?")
			params.append(str(current_channel_id))
		elif is_dm and current_channel_id is not None:
			# Restrict DM searches to same DM channel when respecting DM scope
			where_clauses.append("channel_type = 'dm' AND channel_id = ?")
			params.append(str(current_channel_id))
		# Build query
		where_sql = (" WHERE " + " AND ".join(where_clauses)) if where_clauses else ""
		sql = f"""
			SELECT rowid, content, role, user_id, channel_type, channel_id, timestamp
			FROM interactions_fts
			{where_sql}
			ORDER BY bm25(interactions_fts) ASC
			LIMIT ?
		"""
		params.append(limit)
		cur.execute(sql, params)
		rows = cur.fetchall()
		return [
			{
				"rowid": r[0],
				"content": r[1] or "",
				"role": r[2] or "",
				"user_id": r[3],
				"channel_type": r[4],
				"channel_id": r[5],
				"timestamp": r[6] or 0.0,
			}
			for r in rows
		]
	finally:
		if conn:
			conn.close()

async def get_relevant_memory_context(
	query_text: str,
	current_user_id: Optional[int],
	current_channel_id: Optional[int],
	is_dm_channel: bool
) -> str:
	"""Retrieve and format relevant memory from past conversation logs.

	Returns a newline-joined string of concise snippets, or an empty string if nothing relevant.
	"""
	await initialize_conversation_memory()
	if not query_text:
		return ""
	# Optional query rewrite for recall
	rewritten = query_text
	if RAG_ENABLE_QUERY_REWRITE:
		try:
			rewritten = await rewrite_query_for_rag(query_text, current_user_id)
		except Exception as e:
			logger.warning(f"RAG query rewrite failed, using original: {e}")
	# Detect query intent for precise database filtering
	target_user_id = None
	target_role = None
	
	# If query mentions "my/mine", target the current user specifically
	if current_user_id is not None and any(word in query_text.lower() for word in ['my', 'mine']):
		target_user_id = current_user_id
	# If query mentions "your/yours" or "Luna", target assistant responses
	elif any(word in query_text.lower() for word in ['your', 'yours', 'luna']):
		target_role = 'assistant'
	
	fts_query = _build_fts_query(rewritten)
	loop = asyncio.get_event_loop()
	candidates: List[Dict[str, Any]] = await loop.run_in_executor(
		None,
		_fts_search_sync,
		fts_query,
		RAG_FTS_MAX_CANDIDATES,
		str(current_channel_id) if current_channel_id is not None else None,
		is_dm_channel,
		RAG_REQUIRE_SAME_CHANNEL,
		str(current_channel_id) if current_channel_id is not None else None,
		target_user_id,
		target_role,
	)
	if not candidates:
		return ""
	# Re-rank in Python
	query_tokens = _tokenize(rewritten)
	now_ts = time.time()
	for c in candidates:
		c["_score"] = _score_candidate(
			query_tokens=query_tokens,
			doc_text=c.get("content", ""),
			role=c.get("role", ""),
			same_channel=(str(c.get("channel_id")) == str(current_channel_id)) if current_channel_id is not None else False,
			same_user=(str(c.get("user_id")) == str(current_user_id)) if current_user_id is not None else False,
			now_ts=now_ts,
			msg_ts=float(c.get("timestamp") or 0.0),
		)
	# Filter by threshold and sort
	filtered = [c for c in candidates if c["_score"] >= RAG_MIN_SCORE_THRESHOLD]
	filtered.sort(key=lambda x: x["_score"], reverse=True)
	top = filtered[: RAG_TOP_K]
	if not top:
		return ""
	# Build concise context within budget
	used = 0
	snippets: List[str] = []
	for c in top:
		content = (c.get("content") or "").strip()
		if not content:
			continue
		# Clean any Gemma tags if present
		if content.startswith("<start_of_turn>") and "<end_of_turn>" in content:
			inner = content.split("<start_of_turn>", 1)[1]
			inner = inner.split("<end_of_turn>", 1)[0]
			if "\n" in inner:
				content = inner.split("\n", 1)[1]
			else:
				content = inner
		# If this is a user message that mentions Luna, try to append Luna's reply
		try:
			role_val = c.get("role", "")
			# Append assistant reply for user memories that overlap with query tokens
			if role_val != "assistant":
				doc_toks = set(_tokenize(content))
				if doc_toks and set(query_tokens) & doc_toks:
					assistant_reply = _fetch_next_assistant_reply(int(c.get("rowid", 0) or 0), c.get("channel_id"))
					if assistant_reply:
						content = f"{content}\nLuna: {assistant_reply}"
		except Exception:
			pass
		# Truncate individual snippet
		max_per = max(100, RAG_MAX_CONTEXT_CHARS // max(1, RAG_TOP_K))
		if len(content) > max_per:
			content = content[: max_per - 1] + "…"
		# Speaker label
		try:
			uid = int(c.get("user_id")) if c.get("user_id") is not None else None
			name = get_main_name_by_id(uid) if uid is not None else None
		except Exception:
			name = None
		label = name or (f"User_{c.get('user_id')}" if c.get('user_id') else "Someone")
		# Relative age
		age_days = max(0.0, (now_ts - float(c.get("timestamp") or 0.0)) / 86400.0)
		age_str = "today" if age_days < 1 else (f"{int(age_days)}d ago")
		snippet = f"{label} ({age_str}): {content}"
		if used + len(snippet) + 1 > RAG_MAX_CONTEXT_CHARS:
			break
		snippets.append(snippet)
		used += len(snippet) + 1
	# Ensure minimum size by adding more if available
	if used < RAG_MIN_CONTEXT_CHARS and len(filtered) > len(top):
		for c in filtered[len(top):]:
			content = (c.get("content") or "").strip()
			if not content:
				continue
			max_per = max(80, (RAG_MAX_CONTEXT_CHARS - used) // 2)
			if len(content) > max_per:
				content = content[: max_per - 1] + "…"
			age_days = max(0.0, (now_ts - float(c.get("timestamp") or 0.0)) / 86400.0)
			age_str = "today" if age_days < 1 else (f"{int(age_days)}d ago")
			snippets.append(f"Context ({age_str}): {content}")
			used += len(snippets[-1]) + 1
			if used >= RAG_MIN_CONTEXT_CHARS or used >= RAG_MAX_CONTEXT_CHARS:
				break
	return "\n".join(snippets)

async def rewrite_query_for_rag(original_query: str, current_user_id: Optional[int] = None) -> str:
	"""Rewrites a user query into a better format for RAG retrieval using an LLM."""
	if not original_query:
		return ""
	
	# Get the actual username for more specific search
	username = "user"  # fallback
	if current_user_id is not None:
		try:
			name = get_main_name_by_id(current_user_id)
			if name:
				username = name
		except Exception:
			pass  # Keep fallback
	
	# Use shared llama.cpp model with global lock to avoid GPU violations
	try:
		# Reuse the AMD-based brain/decision instance to avoid a third LLM
		model = get_gemma_cpp_client()
		prompt = (
			f"Rewrite to search keywords. Replace 'my' with '{username}' and 'your' with 'Luna'.\n\n"
			f"Q: Luna, what's my favorite food?\n"
			f"A: {username} favorite food\n\n"
			f"Q: What's your favorite color?\n"
			f"A: Luna favorite color\n\n"
			f"Q: {original_query}\n"
			f"A:"
		)
		rewrite_start_time = time.monotonic()
		logger.info(f"🔄 RAG QUERY REWRITE: Starting rewrite for '{original_query}' (username: {username})")
		logger.debug(f"RAG rewrite prompt: {prompt}")
		
		result = call_model_safe(
			model,
			prompt,
			max_tokens=100,  # Increased from 50 to give model room to respond
			temperature=REWRITE_TEMP,
			repeat_penalty=1.0,  # Disabled to allow pattern matching
			stop=["\n", "<end_of_turn>"]
		)
		rewrite_end_time = time.monotonic()
		logger.info(f"⚡ RAG QUERY REWRITE: Completed in {rewrite_end_time - rewrite_start_time:.6f}s")
		
		text = ""
		if isinstance(result, dict):
			text = result.get("choices", [{}])[0].get("text", "")
		else:
			text = str(result)
		
		logger.debug(f"RAG rewrite raw result: '{text}'")
		rewritten_query = (text or "").strip().strip('"').replace('"', '')
		
		if rewritten_query and rewritten_query.lower() != original_query.lower():
			logger.info(f"✅ RAG QUERY REWRITE SUCCESS: '{original_query}' -> '{rewritten_query}'")
			return rewritten_query
		
		logger.warning(f"⚠️ RAG QUERY REWRITE FAILED: Empty/identical result. Using original: '{original_query}'")
		logger.debug(f"Raw result was: '{text}' -> cleaned: '{rewritten_query}'")
		return original_query
	except Exception as e:
		logger.error(f"Error rewriting query '{original_query}': {e}", exc_info=True)
		return original_query


def format_conversation_history_for_prompt(history: list, max_entries=10) -> str:
	"""Formats the last few history entries for inclusion in a prompt."""
	if not history:
		return "No recent history available."
	formatted_lines = []
	# Take the last 'max_entries' from the history
	recent_history = history[-max_entries:]
	for msg in recent_history:
		role = msg.get("role", "unknown")
		content = msg.get("content", "")
		user_id = msg.get("user_id", "") # Keep user_id for potential context
		# Basic formatting, could add timestamps or user names if needed later
		# Use a more descriptive role label
		role_label = "Luna" if role == "assistant" else f"User ({user_id})"
		formatted_lines.append(f"{role_label}: {content}")
	return "\n".join(formatted_lines)