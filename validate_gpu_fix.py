#!/usr/bin/env python3
"""
Simple validation test for GPU access violation fix.
This confirms the global GPU context lock prevents cross-backend memory conflicts.
"""

import asyncio
import time
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_gpu_fix_validation():
    """Validate the GPU access violation fix with direct model calls"""
    try:
        from llm_response.initialization import get_gemma3_client, get_qwen3_client
        from shared_model import call_model_safe
        
        logger.info("🧪 VALIDATING GPU ACCESS VIOLATION FIX")
        print("=" * 60)
        
        # Initialize both models
        logger.info("Initializing models...")
        nvidia_model = get_gemma3_client()
        amd_model = get_qwen3_client()
        
        # Test 1: Sequential calls (baseline)
        logger.info("Test 1: Sequential GPU usage...")
        start_time = time.time()
        
        nvidia_result = call_model_safe(
            model=nvidia_model,
            prompt="Test 1: What is 1+1?",
            gpu_target="nvidia_4070",
            max_tokens=10,
            temperature=0.1
        )
        
        amd_result = call_model_safe(
            model=amd_model,
            prompt="Test 1: What is 2+2?",
            gpu_target="amd_6650xt",
            max_tokens=10,
            temperature=0.1
        )
        
        sequential_time = time.time() - start_time
        logger.info(f"✅ Sequential test: PASS ({sequential_time:.2f}s)")
        
        # Test 2: Concurrent calls (the real test)
        logger.info("Test 2: Concurrent GPU usage (stress test)...")
        start_time = time.time()
        
        async def concurrent_nvidia():
            return call_model_safe(
                model=nvidia_model,
                prompt="Concurrent test: What is 3+3?",
                gpu_target="nvidia_4070",
                max_tokens=15,
                temperature=0.1
            )
        
        async def concurrent_amd():
            return call_model_safe(
                model=amd_model,
                prompt="Concurrent test: What is 4+4?",
                gpu_target="amd_6650xt",
                max_tokens=15,
                temperature=0.1
            )
        
        # Launch both concurrently
        nvidia_task = asyncio.create_task(asyncio.to_thread(concurrent_nvidia))
        amd_task = asyncio.create_task(asyncio.to_thread(concurrent_amd))
        
        nvidia_result, amd_result = await asyncio.gather(nvidia_task, amd_task)
        
        concurrent_time = time.time() - start_time
        logger.info(f"✅ Concurrent test: PASS ({concurrent_time:.2f}s)")
        
        # Test 3: Rapid switching
        logger.info("Test 3: Rapid GPU context switching...")
        start_time = time.time()
        
        for i in range(3):
            # Alternate between GPUs rapidly
            call_model_safe(nvidia_model, f"Switch {i}: NVIDIA test", "nvidia_4070", max_tokens=5, temperature=0.1)
            call_model_safe(amd_model, f"Switch {i}: AMD test", "amd_6650xt", max_tokens=5, temperature=0.1)
        
        switching_time = time.time() - start_time
        logger.info(f"✅ Rapid switching: PASS ({switching_time:.2f}s)")
        
        # Results
        print("\n" + "=" * 60)
        print("🎉 GPU ACCESS VIOLATION FIX VALIDATION: SUCCESS")
        print("=" * 60)
        print(f"Sequential usage:    {sequential_time:.2f}s")
        print(f"Concurrent usage:    {concurrent_time:.2f}s") 
        print(f"Rapid switching:     {switching_time:.2f}s")
        print("\n✅ No access violations detected!")
        print("✅ Cross-GPU memory isolation working!")
        print("✅ Global GPU context lock effective!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ VALIDATION FAILED: {e}")
        logger.error(f"GPU fix validation failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_gpu_fix_validation())
