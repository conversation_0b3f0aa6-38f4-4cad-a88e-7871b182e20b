#!/usr/bin/env python3
"""
Debug Failing Queries
====================
Debug the two queries that are still failing
"""
import asyncio
import sqlite3
from llm_response.memory_system import get_memory_system, initialize_memory_system

async def debug_failing_queries():
    print("🔍 Debugging Failing Queries")
    print("=" * 40)
    
    await initialize_memory_system()
    ms = get_memory_system()
    user_id = 921637353364287489
    
    failing_queries = [
        ("<PERSON>, what's my favorite color?", "Should find <PERSON>'s color fact"),
        ("<PERSON>, what's your favorite block in Minecraft?", "Should find <PERSON>'s block fact")
    ]
    
    conn = sqlite3.connect(ms.warm_storage.db_path)
    try:
        for query, expected in failing_queries:
            print(f"\n🔍 Query: '{query}'")
            print(f"   Expected: {expected}")
            
            # Test different search approaches
            search_terms = [
                "luna my favorite color",
                "my favorite color", 
                "favorite color",
                "luna your favorite block minecraft",
                "favorite block minecraft",
                "minecraft block",
                "block"
            ]
            
            for search_term in search_terms:
                if any(word in search_term for word in query.lower().split()):
                    # Test FTS search
                    cursor = conn.execute("""
                        SELECT mf.content, mf.user_id, mf.confidence
                        FROM memory_facts mf
                        WHERE mf.rowid IN (SELECT rowid FROM memory_facts_fts WHERE memory_facts_fts MATCH ?)
                        ORDER BY mf.confidence DESC
                        LIMIT 3
                    """, (f'"{search_term.replace(" ", '" AND "')}"',))
                    
                    results = cursor.fetchall()
                    if results:
                        print(f"   ✅ FTS '{search_term}' -> {len(results)} results:")
                        for result in results:
                            print(f"     - {result[0][:50]}... (user: {result[1]})")
                        break
            else:
                print(f"   ❌ No FTS results for any search variation")
    
    finally:
        conn.close()

if __name__ == "__main__":
    asyncio.run(debug_failing_queries())