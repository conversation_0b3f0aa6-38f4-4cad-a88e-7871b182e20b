# 🌐 Web Search Configuration Guide

This document explains all the configurable options for <PERSON>'s web search system and the detailed summarization process.

## 📋 **Configuration Options**

All configuration options can be set in your `local.env` file. If not set, the system uses sensible defaults.

### **Search Behavior**
```env
# Maximum number of search results to retrieve from Google API
WEB_SEARCH_MAX_RESULTS=5

# Number of top results to use when creating the summary (usually 3-5 is optimal)
WEB_SEARCH_RESULTS_FOR_SUMMARY=3
```

### **Cache Settings**
```env
# How many days to keep cached search results before they expire
WEB_SEARCH_CACHE_EXPIRY_DAYS=7

# Maximum number of searches to keep in cache before cleanup
WEB_SEARCH_MAX_CACHE_SIZE=1000

# Similarity threshold for FAISS vector matching (0.0-1.0, higher = more strict)
WEB_SEARCH_SIMILARITY_THRESHOLD=0.85
```

### **Summarization Quality**
```env
# Maximum tokens for the summary generated by <PERSON>'s brain
WEB_SEARCH_SUMMARY_MAX_TOKENS=150

# Temperature for summary generation (0.0-1.0, lower = more focused)
WEB_SEARCH_SUMMARY_TEMPERATURE=0.3
```

## 🔄 **Detailed Summarization Process**

Here's exactly how Luna processes web searches:

### **Phase 1: Detection & Intent Recognition**
1. **User Input**: *"Luna, search the web for latest AI news"*
2. **Brain Analysis**: Luna's brain system (llama.cpp) analyzes the message
3. **Intent Detection**: Brain outputs `"WebSearch latest AI news"` 
4. **Parsing**: Processing system extracts the query: `"latest AI news"`

### **Phase 2: Search Execution**
5. **Cache Check**: FAISS vector search checks for similar queries (85% similarity)
6. **API Call**: If no cache hit, calls Google Custom Search API
7. **Results Retrieved**: Gets top 5 results (title, snippet, URL) from Google

### **Phase 3: Intelligent Summarization** 🧠
8. **Result Selection**: Takes top 3 results (configurable) for summarization
9. **Context Formatting**: Formats results into structured prompt:
   ```
   Based on these search results for "latest AI news", provide a concise 
   and helpful summary that Luna can use to respond to the user.
   
   SEARCH RESULTS:
   1. **OpenAI Announces GPT-5**
      New breakthrough in AI reasoning capabilities...
      URL: https://example.com
   
   2. **Google's Gemini Update**  
      Enhanced multimodal AI features released...
      URL: https://example.com
   
   SUMMARY (2-3 sentences max):
   ```

10. **Brain Summarization**: Luna's brain model (same llama.cpp model) processes this
11. **Thread-Safe Call**: Uses `call_model_safe()` to prevent crashes
12. **Generated Summary**: Returns natural, conversational summary in Luna's style

### **Phase 4: Context Integration**
13. **Context Injection**: Summary is injected into processing.py as system context
14. **Natural Response**: Luna's main conversation model receives the summary as background knowledge
15. **User Response**: Luna responds naturally as if she found the information herself

### **Phase 5: Caching & Performance**
16. **Vector Storage**: Query embedding stored in FAISS for future similarity matching
17. **Cache Persistence**: Results saved to disk for future sessions
18. **Performance Tracking**: Latency metrics recorded for optimization

## 🎯 **Example Flow with Configuration**

**Configuration:**
- `WEB_SEARCH_MAX_RESULTS=5` (Get 5 results from Google)
- `WEB_SEARCH_RESULTS_FOR_SUMMARY=3` (Use top 3 for summary)
- `WEB_SEARCH_SUMMARY_MAX_TOKENS=150` (Max 150 tokens for summary)
- `WEB_SEARCH_SUMMARY_TEMPERATURE=0.3` (Focused, accurate summary)

**Process:**
1. Google API returns 5 results
2. Brain model sees top 3 results in prompt
3. Generates ~150 token summary at 0.3 temperature
4. Summary cached for 7 days
5. Luna responds with natural conversation using summary context

## ⚡ **Performance Benefits**

- **Cache Hit Rate**: ~70-80% of similar queries served from cache
- **Response Time**: Cached queries respond in <1ms
- **API Efficiency**: Reduced Google API calls through intelligent caching
- **Memory Usage**: Vector similarity prevents storing duplicate content

## 🔧 **Tuning Recommendations**

### **For More Detailed Summaries:**
```env
WEB_SEARCH_RESULTS_FOR_SUMMARY=5
WEB_SEARCH_SUMMARY_MAX_TOKENS=200
WEB_SEARCH_SUMMARY_TEMPERATURE=0.4
```

### **For Faster, Concise Summaries:**
```env
WEB_SEARCH_RESULTS_FOR_SUMMARY=2
WEB_SEARCH_SUMMARY_MAX_TOKENS=100
WEB_SEARCH_SUMMARY_TEMPERATURE=0.2
```

### **For Aggressive Caching:**
```env
WEB_SEARCH_SIMILARITY_THRESHOLD=0.75
WEB_SEARCH_CACHE_EXPIRY_DAYS=14
WEB_SEARCH_MAX_CACHE_SIZE=2000
```

## 🚨 **Critical Notes**

1. **Thread Safety**: The summarization uses `call_model_safe()` to prevent crashes
2. **Brain Model**: Uses the same llama.cpp model as Luna's brain for consistency
3. **Context Integration**: Summary becomes part of Luna's knowledge for natural responses
4. **Error Handling**: Graceful fallbacks if summarization fails

The system is designed to make Luna appear as if she naturally knows current information, rather than obviously "searching the web" - the summarization process is key to this natural integration! 🌟
